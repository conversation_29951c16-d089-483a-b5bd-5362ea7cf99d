"""
Patient views for Quant-NEX Healthcare Application.
"""
from rest_framework import status, generics, permissions, viewsets
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.response import Response
from django.db.models import Q, Count
from django.utils import timezone
from datetime import datetime, timedelta
from .models import Patient, MedicalRecord, Appointment
from .serializers import (
    PatientSerializer, PatientListSerializer, MedicalRecordSerializer,
    AppointmentSerializer, PatientStatsSerializer, PatientSearchSerializer,
    VitalSignsSerializer
)


class PatientViewSet(viewsets.ModelViewSet):
    """
    Patient CRUD operations.
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        if self.action == 'list':
            return PatientListSerializer
        return PatientSerializer
    
    def get_queryset(self):
        user = self.request.user
        queryset = Patient.objects.all()
        
        # Filter by assigned doctor if user is a doctor
        if user.role == 'doctor':
            queryset = queryset.filter(assigned_doctor=user)
        
        # Apply search filters
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(patient_id__icontains=search) |
                Q(first_name__icontains=search) |
                Q(last_name__icontains=search) |
                Q(phone_number__icontains=search) |
                Q(email__icontains=search)
            )
        
        # Apply status filter
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        return queryset.order_by('-created_at')
    
    @action(detail=True, methods=['get'])
    def medical_records(self, request, pk=None):
        """Get patient's medical records."""
        patient = self.get_object()
        records = MedicalRecord.objects.filter(patient=patient).order_by('-created_at')
        serializer = MedicalRecordSerializer(records, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def appointments(self, request, pk=None):
        """Get patient's appointments."""
        patient = self.get_object()
        appointments = Appointment.objects.filter(patient=patient).order_by('-scheduled_date')
        serializer = AppointmentSerializer(appointments, many=True)
        return Response(serializer.data)


class MedicalRecordListCreateView(generics.ListCreateAPIView):
    """
    List and create medical records for a patient.
    """
    serializer_class = MedicalRecordSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        patient_id = self.kwargs['patient_id']
        return MedicalRecord.objects.filter(patient_id=patient_id).order_by('-created_at')
    
    def perform_create(self, serializer):
        patient_id = self.kwargs['patient_id']
        serializer.save(
            patient_id=patient_id,
            doctor=self.request.user
        )


class MedicalRecordDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update, or delete a medical record.
    """
    queryset = MedicalRecord.objects.all()
    serializer_class = MedicalRecordSerializer
    permission_classes = [permissions.IsAuthenticated]


class AppointmentListCreateView(generics.ListCreateAPIView):
    """
    List and create appointments.
    """
    serializer_class = AppointmentSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        user = self.request.user
        queryset = Appointment.objects.all()
        
        # Filter by doctor if user is a doctor
        if user.role == 'doctor':
            queryset = queryset.filter(doctor=user)
        
        # Apply date filters
        date_from = self.request.query_params.get('date_from')
        date_to = self.request.query_params.get('date_to')
        
        if date_from:
            queryset = queryset.filter(scheduled_date__gte=date_from)
        if date_to:
            queryset = queryset.filter(scheduled_date__lte=date_to)
        
        return queryset.order_by('scheduled_date', 'scheduled_time')


class AppointmentDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update, or delete an appointment.
    """
    queryset = Appointment.objects.all()
    serializer_class = AppointmentSerializer
    permission_classes = [permissions.IsAuthenticated]


class PatientAppointmentListView(generics.ListAPIView):
    """
    List appointments for a specific patient.
    """
    serializer_class = AppointmentSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        patient_id = self.kwargs['patient_id']
        return Appointment.objects.filter(patient_id=patient_id).order_by('-scheduled_date')


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def patient_statistics(request):
    """
    Get patient statistics.
    """
    user = request.user
    
    # Base queryset based on user role
    if user.role == 'doctor':
        patients = Patient.objects.filter(assigned_doctor=user)
    else:
        patients = Patient.objects.all()
    
    # Calculate statistics
    total_patients = patients.count()
    active_patients = patients.filter(status='active').count()
    critical_patients = patients.filter(status='critical').count()
    
    # New patients this month
    today = timezone.now().date()
    new_patients_this_month = patients.filter(
        created_at__month=today.month,
        created_at__year=today.year
    ).count()
    
    # Status distribution
    status_distribution = patients.values('status').annotate(
        count=Count('id')
    ).order_by('status')
    
    # Cancer type distribution
    cancer_distribution = patients.exclude(
        cancer_type__isnull=True
    ).exclude(
        cancer_type__exact=''
    ).values('cancer_type').annotate(
        count=Count('id')
    ).order_by('-count')[:10]
    
    # Gender distribution
    gender_distribution = patients.values('gender').annotate(
        count=Count('id')
    ).order_by('gender')
    
    # Calculate average age
    ages = []
    for patient in patients:
        ages.append(patient.age)
    average_age = sum(ages) / len(ages) if ages else 0
    
    return Response({
        'total_patients': total_patients,
        'active_patients': active_patients,
        'critical_patients': critical_patients,
        'new_patients_this_month': new_patients_this_month,
        'patients_by_status': list(status_distribution),
        'patients_by_cancer_type': list(cancer_distribution),
        'average_age': round(average_age, 1),
        'gender_distribution': list(gender_distribution),
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def patient_search(request):
    """
    Advanced patient search.
    """
    serializer = PatientSearchSerializer(data=request.query_params)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    data = serializer.validated_data
    user = request.user
    
    # Base queryset
    if user.role == 'doctor':
        queryset = Patient.objects.filter(assigned_doctor=user)
    else:
        queryset = Patient.objects.all()
    
    # Apply filters
    if data.get('query'):
        query = data['query']
        queryset = queryset.filter(
            Q(patient_id__icontains=query) |
            Q(first_name__icontains=query) |
            Q(last_name__icontains=query) |
            Q(phone_number__icontains=query) |
            Q(email__icontains=query)
        )
    
    if data.get('status'):
        queryset = queryset.filter(status=data['status'])
    
    if data.get('cancer_type'):
        queryset = queryset.filter(cancer_type__icontains=data['cancer_type'])
    
    if data.get('assigned_doctor'):
        queryset = queryset.filter(assigned_doctor_id=data['assigned_doctor'])
    
    if data.get('gender'):
        queryset = queryset.filter(gender=data['gender'])
    
    # Age filtering
    if data.get('age_min') or data.get('age_max'):
        today = timezone.now().date()
        if data.get('age_min'):
            max_birth_date = today - timedelta(days=data['age_min'] * 365)
            queryset = queryset.filter(date_of_birth__lte=max_birth_date)
        if data.get('age_max'):
            min_birth_date = today - timedelta(days=data['age_max'] * 365)
            queryset = queryset.filter(date_of_birth__gte=min_birth_date)
    
    # Serialize results
    patients = queryset.order_by('-created_at')
    serializer = PatientListSerializer(patients, many=True)
    
    return Response({
        'count': patients.count(),
        'results': serializer.data
    })


@api_view(['GET', 'POST'])
@permission_classes([permissions.IsAuthenticated])
def patient_vital_signs(request, patient_id):
    """
    Get or add vital signs for a patient.
    """
    try:
        patient = Patient.objects.get(id=patient_id)
    except Patient.DoesNotExist:
        return Response(
            {'error': 'Patient not found'}, 
            status=status.HTTP_404_NOT_FOUND
        )
    
    if request.method == 'GET':
        # Get recent vital signs from medical records
        records = MedicalRecord.objects.filter(
            patient=patient,
            record_type='consultation'
        ).exclude(
            temperature__isnull=True,
            blood_pressure_systolic__isnull=True,
            heart_rate__isnull=True
        ).order_by('-created_at')[:10]
        
        vital_signs = []
        for record in records:
            vital_signs.append({
                'temperature': record.temperature,
                'blood_pressure_systolic': record.blood_pressure_systolic,
                'blood_pressure_diastolic': record.blood_pressure_diastolic,
                'heart_rate': record.heart_rate,
                'respiratory_rate': record.respiratory_rate,
                'oxygen_saturation': record.oxygen_saturation,
                'recorded_at': record.created_at,
            })
        
        return Response(vital_signs)
    
    elif request.method == 'POST':
        # Add new vital signs
        serializer = VitalSignsSerializer(data=request.data)
        if serializer.is_valid():
            # Create a medical record with vital signs
            MedicalRecord.objects.create(
                patient=patient,
                doctor=request.user,
                record_type='consultation',
                title='Vital Signs Check',
                description='Routine vital signs measurement',
                **serializer.validated_data
            )
            return Response(
                {'message': 'Vital signs recorded successfully'}, 
                status=status.HTTP_201_CREATED
            )
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
