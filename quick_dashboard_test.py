import requests

# Quick test
login_data = {"username": "admin", "password": "quantnex123"}
response = requests.post("http://localhost:8000/api/auth/login/", json=login_data)

if response.status_code == 200:
    token = response.json()['access']
    headers = {"Authorization": f"Bearer {token}"}
    
    # Test dashboard overview
    dashboard_response = requests.get("http://localhost:8000/api/dashboard/overview/", headers=headers)
    print(f"Dashboard Overview: {dashboard_response.status_code}")
    
    if dashboard_response.status_code == 200:
        print("✅ Dashboard Overview working!")
    else:
        print(f"❌ Dashboard Overview error: {dashboard_response.text[:200]}")
else:
    print("❌ Login failed")
