"""
Diagnosis URLs for Quant-NEX Healthcare Application.
"""
from django.urls import path
from . import views

urlpatterns = [
    # Diagnosis management
    path('', views.DiagnosisListCreateView.as_view(), name='diagnosis-list'),
    path('<int:pk>/', views.DiagnosisDetailView.as_view(), name='diagnosis-detail'),
    
    # Patient-specific diagnoses
    path('patient/<int:patient_id>/', views.PatientDiagnosisListView.as_view(), name='patient-diagnoses'),
    
    # Diagnostic tests
    path('tests/', views.DiagnosticTestListCreateView.as_view(), name='diagnostic-test-list'),
    path('tests/<int:pk>/', views.DiagnosticTestDetailView.as_view(), name='diagnostic-test-detail'),
    
    # Imaging studies
    path('imaging/', views.ImagingStudyListCreateView.as_view(), name='imaging-study-list'),
    path('imaging/<int:pk>/', views.ImagingStudyDetailView.as_view(), name='imaging-study-detail'),
    
    # 3D Model data
    path('imaging/<int:study_id>/model/', views.get_3d_model_data, name='3d-model-data'),
]
