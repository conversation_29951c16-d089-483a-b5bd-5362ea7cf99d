"""
Authentication admin configuration for Quant-NEX Healthcare Application.
"""
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from .models import User, UserPreferences, UserSession


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """
    Custom user admin with healthcare-specific fields.
    """
    list_display = [
        'username', 'email', 'display_name', 'role', 'specialization',
        'is_verified', 'is_active', 'date_joined'
    ]
    list_filter = [
        'role', 'specialization', 'is_verified', 'is_active', 'date_joined'
    ]
    search_fields = ['username', 'email', 'first_name', 'last_name', 'license_number']
    ordering = ['-date_joined']
    
    fieldsets = BaseUserAdmin.fieldsets + (
        ('Professional Information', {
            'fields': ('role', 'specialization', 'license_number', 'department', 'hospital_name')
        }),
        ('Contact Information', {
            'fields': ('phone_number', 'emergency_contact')
        }),
        ('Profile Information', {
            'fields': ('middle_name', 'profile_picture', 'bio', 'years_of_experience')
        }),
        ('System Information', {
            'fields': ('is_verified', 'last_login_ip')
        }),
    )
    
    add_fieldsets = BaseUserAdmin.add_fieldsets + (
        ('Professional Information', {
            'fields': ('role', 'specialization', 'license_number')
        }),
    )


@admin.register(UserPreferences)
class UserPreferencesAdmin(admin.ModelAdmin):
    """
    User preferences admin.
    """
    list_display = ['user', 'theme', 'language', 'enable_4d_features']
    list_filter = ['theme', 'language', 'enable_4d_features']
    search_fields = ['user__username', 'user__email']


@admin.register(UserSession)
class UserSessionAdmin(admin.ModelAdmin):
    """
    User session admin.
    """
    list_display = ['user', 'ip_address', 'login_time', 'logout_time', 'is_active']
    list_filter = ['is_active', 'login_time']
    search_fields = ['user__username', 'ip_address']
    readonly_fields = ['session_key', 'user_agent', 'login_time', 'logout_time']
