#!/usr/bin/env python3
"""
Integration test script for Quant-NEX Full-Stack Application
Tests Django backend and Next.js frontend integration
"""

import requests
import json
import time
import sys

def test_django_backend():
    """Test Django backend API endpoints"""
    print("🔍 Testing Django Backend...")
    
    base_url = "http://localhost:8000/api"
    
    # Test 1: API Documentation
    try:
        response = requests.get("http://localhost:8000/api/docs/")
        if response.status_code == 200:
            print("✅ API Documentation accessible")
        else:
            print(f"❌ API Documentation failed: {response.status_code}")
    except Exception as e:
        print(f"❌ API Documentation error: {e}")
    
    # Test 2: Login endpoint
    try:
        login_data = {"username": "admin", "password": "quantnex123"}
        response = requests.post(f"{base_url}/auth/login/", json=login_data)
        
        if response.status_code == 200:
            data = response.json()
            access_token = data.get('access')
            print("✅ Login endpoint working")
            print(f"   User: {data.get('user', {}).get('display_name', 'Unknown')}")
            
            # Test 3: Authenticated endpoints
            headers = {"Authorization": f"Bearer {access_token}"}
            
            # Test dashboard overview
            dashboard_response = requests.get(f"{base_url}/dashboard/overview/", headers=headers)
            if dashboard_response.status_code == 200:
                print("✅ Dashboard overview endpoint working")
            else:
                print(f"❌ Dashboard overview failed: {dashboard_response.status_code}")
            
            # Test patient list
            patients_response = requests.get(f"{base_url}/patients/", headers=headers)
            if patients_response.status_code == 200:
                print("✅ Patients endpoint working")
            else:
                print(f"❌ Patients endpoint failed: {patients_response.status_code}")
            
            return True
        else:
            print(f"❌ Login failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Backend test error: {e}")
        return False

def test_frontend():
    """Test Next.js frontend"""
    print("\n🔍 Testing Next.js Frontend...")
    
    try:
        # Test frontend homepage
        response = requests.get("http://localhost:3000")
        if response.status_code == 200:
            print("✅ Frontend homepage accessible")
        else:
            print(f"❌ Frontend homepage failed: {response.status_code}")
            return False
        
        # Test login page
        response = requests.get("http://localhost:3000/login")
        if response.status_code == 200:
            print("✅ Login page accessible")
        else:
            print(f"❌ Login page failed: {response.status_code}")
        
        # Test dashboard page
        response = requests.get("http://localhost:3000/dashboard")
        if response.status_code == 200:
            print("✅ Dashboard page accessible")
        else:
            print(f"❌ Dashboard page failed: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ Frontend test error: {e}")
        return False

def test_cors():
    """Test CORS configuration"""
    print("\n🔍 Testing CORS Configuration...")
    
    try:
        # Simulate a CORS request from frontend to backend
        headers = {
            'Origin': 'http://localhost:3000',
            'Access-Control-Request-Method': 'POST',
            'Access-Control-Request-Headers': 'Content-Type,Authorization'
        }
        
        response = requests.options("http://localhost:8000/api/auth/login/", headers=headers)
        
        if response.status_code in [200, 204]:
            cors_headers = response.headers
            if 'Access-Control-Allow-Origin' in cors_headers:
                print("✅ CORS properly configured")
                print(f"   Allowed Origin: {cors_headers.get('Access-Control-Allow-Origin')}")
                return True
            else:
                print("❌ CORS headers missing")
                return False
        else:
            print(f"❌ CORS preflight failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ CORS test error: {e}")
        return False

def main():
    """Run all integration tests"""
    print("🏥 Quant-NEX Full-Stack Integration Test")
    print("=" * 50)
    
    # Test backend
    backend_ok = test_django_backend()
    
    # Test frontend
    frontend_ok = test_frontend()
    
    # Test CORS
    cors_ok = test_cors()
    
    # Summary
    print("\n📊 Test Results Summary:")
    print("=" * 30)
    print(f"Backend API: {'✅ PASS' if backend_ok else '❌ FAIL'}")
    print(f"Frontend:    {'✅ PASS' if frontend_ok else '❌ FAIL'}")
    print(f"CORS:        {'✅ PASS' if cors_ok else '❌ FAIL'}")
    
    if backend_ok and frontend_ok and cors_ok:
        print("\n🎉 All tests passed! Full-stack integration is working correctly.")
        print("\nNext steps:")
        print("1. Open http://localhost:3000 in your browser")
        print("2. Login with: admin / quantnex123")
        print("3. Test the application functionality")
        return 0
    else:
        print("\n⚠️  Some tests failed. Please check the errors above.")
        return 1

if __name__ == '__main__':
    sys.exit(main())
