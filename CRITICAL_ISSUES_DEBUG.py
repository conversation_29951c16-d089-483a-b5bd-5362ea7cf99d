#!/usr/bin/env python3
"""
CRITICAL ISSUES DEBUGGING for Quant-NEX
Tests all reported critical issues systematically
"""

import requests
import json
import time

def test_authentication_issues():
    """Test authentication functionality"""
    print("🔐 TESTING AUTHENTICATION ISSUES")
    print("=" * 50)
    
    # Test 1: Django API Login
    print("1. Testing Django API Login...")
    try:
        response = requests.post("http://localhost:8000/api/auth/login/", 
                               json={"username": "admin", "password": "quantnex123"},
                               timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Django API Login: WORKING")
            print(f"   User: {data['user']['display_name']}")
            print(f"   Token: {data['access'][:30]}...")
            return data['access']
        else:
            print(f"❌ Django API Login: FAILED ({response.status_code})")
            print(f"   Response: {response.text[:200]}")
            return None
    except Exception as e:
        print(f"❌ Django API Login: ERROR - {e}")
        return None

def test_registration_issues():
    """Test registration functionality"""
    print("\n📝 TESTING REGISTRATION ISSUES")
    print("=" * 50)
    
    # Test registration endpoint
    print("1. Testing Django API Registration...")
    try:
        test_user_data = {
            "username": "<EMAIL>",
            "email": "<EMAIL>", 
            "password": "testpass123",
            "password_confirm": "testpass123",
            "first_name": "Test",
            "last_name": "Doctor",
            "role": "doctor"
        }
        
        response = requests.post("http://localhost:8000/api/auth/register/", 
                               json=test_user_data,
                               timeout=10)
        
        if response.status_code == 201:
            print("✅ Django API Registration: WORKING")
            return True
        elif response.status_code == 400:
            print("⚠️ Django API Registration: User might already exist")
            print(f"   Response: {response.text[:200]}")
            return True  # This is expected if user exists
        else:
            print(f"❌ Django API Registration: FAILED ({response.status_code})")
            print(f"   Response: {response.text[:200]}")
            return False
    except Exception as e:
        print(f"❌ Django API Registration: ERROR - {e}")
        return False

def test_frontend_pages():
    """Test frontend page accessibility"""
    print("\n🌐 TESTING FRONTEND PAGES")
    print("=" * 50)
    
    pages = [
        ("Landing Page", "http://localhost:3000"),
        ("Login Page", "http://localhost:3000/login"),
        ("Dashboard", "http://localhost:3000/dashboard"),
        ("Treatment Page", "http://localhost:3000/treatment"),
        ("Diagnosis Page", "http://localhost:3000/diagnosis"),
    ]
    
    working_pages = 0
    for name, url in pages:
        try:
            response = requests.get(url, timeout=8)
            if response.status_code == 200:
                print(f"✅ {name}: ACCESSIBLE")
                working_pages += 1
            else:
                print(f"❌ {name}: FAILED ({response.status_code})")
        except Exception as e:
            print(f"❌ {name}: ERROR - {e}")
    
    return working_pages >= 4

def test_3d_visualization_dependencies():
    """Test if 3D visualization dependencies are available"""
    print("\n🎮 TESTING 3D VISUALIZATION DEPENDENCIES")
    print("=" * 50)
    
    # Test if Three.js components are accessible
    print("1. Testing 3D component pages...")
    
    # Pages that should have 3D visualizations
    pages_with_3d = [
        ("Landing Page (TumorVisualization3D)", "http://localhost:3000"),
        ("Treatment Page (3D Models)", "http://localhost:3000/treatment"),
        ("Diagnosis Page (3D Models)", "http://localhost:3000/diagnosis"),
    ]
    
    working_3d_pages = 0
    for name, url in pages_with_3d:
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                # Check if the page content suggests 3D components are loading
                content = response.text.lower()
                if any(keyword in content for keyword in ['canvas', 'three', 'webgl', '3d']):
                    print(f"✅ {name}: 3D COMPONENTS DETECTED")
                    working_3d_pages += 1
                else:
                    print(f"⚠️ {name}: PAGE LOADS BUT NO 3D DETECTED")
            else:
                print(f"❌ {name}: PAGE FAILED ({response.status_code})")
        except Exception as e:
            print(f"❌ {name}: ERROR - {e}")
    
    return working_3d_pages >= 2

def test_complete_user_journey():
    """Test complete user journey"""
    print("\n🚀 TESTING COMPLETE USER JOURNEY")
    print("=" * 50)
    
    journey_steps = [
        ("Landing Page Access", "http://localhost:3000"),
        ("Login Page Access", "http://localhost:3000/login"),
        ("Dashboard Access", "http://localhost:3000/dashboard"),
    ]
    
    successful_steps = 0
    for step_name, url in journey_steps:
        try:
            response = requests.get(url, timeout=8)
            if response.status_code == 200:
                print(f"✅ {step_name}: SUCCESS")
                successful_steps += 1
            else:
                print(f"❌ {step_name}: FAILED ({response.status_code})")
        except Exception as e:
            print(f"❌ {step_name}: ERROR - {e}")
    
    return successful_steps == len(journey_steps)

def main():
    """Run all critical issue tests"""
    print("🚨 QUANT-NEX CRITICAL ISSUES DEBUGGING")
    print("=" * 60)
    print("Testing all reported critical issues...")
    print()
    
    # Test 1: Authentication Issues
    auth_token = test_authentication_issues()
    auth_working = auth_token is not None
    
    # Test 2: Registration Issues  
    registration_working = test_registration_issues()
    
    # Test 3: Frontend Pages
    frontend_working = test_frontend_pages()
    
    # Test 4: 3D Visualization Dependencies
    visualization_working = test_3d_visualization_dependencies()
    
    # Test 5: Complete User Journey
    journey_working = test_complete_user_journey()
    
    # Summary Report
    print("\n" + "=" * 60)
    print("🔍 CRITICAL ISSUES ANALYSIS REPORT")
    print("=" * 60)
    
    issues = [
        ("Authentication Functionality", auth_working),
        ("Registration Functionality", registration_working), 
        ("Frontend Page Access", frontend_working),
        ("3D Visualization Components", visualization_working),
        ("Complete User Journey", journey_working),
    ]
    
    working_systems = 0
    for issue_name, is_working in issues:
        status = "✅ WORKING" if is_working else "❌ CRITICAL ISSUE"
        print(f"{issue_name:<30}: {status}")
        if is_working:
            working_systems += 1
    
    print(f"\nOverall System Health: {working_systems}/{len(issues)} systems operational")
    
    if working_systems == len(issues):
        print("\n🎉 ALL SYSTEMS OPERATIONAL!")
        print("The reported issues may be browser-specific or integration-related.")
        print("\n📋 NEXT STEPS:")
        print("1. Test login in browser: http://localhost:3000/login")
        print("2. Use credentials: admin / quantnex123")
        print("3. Check browser console for JavaScript errors")
        print("4. Verify 3D models load correctly")
    else:
        print(f"\n⚠️ {len(issues) - working_systems} CRITICAL ISSUE(S) DETECTED")
        print("Detailed fixes required for failing systems.")
    
    return working_systems == len(issues)

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
