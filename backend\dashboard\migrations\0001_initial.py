# Generated by Django 5.0.1 on 2025-06-19 09:10

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemAlert',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('alert_type', models.CharField(choices=[('info', 'Information'), ('warning', 'Warning'), ('error', 'Error'), ('critical', 'Critical'), ('maintenance', 'Maintenance')], max_length=20)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')], default='medium', max_length=20)),
                ('target_roles', models.JSONField(blank=True, default=list, help_text='List of roles to target')),
                ('is_global', models.BooleanField(default=False, help_text='Show to all users')),
                ('start_time', models.DateTimeField()),
                ('end_time', models.DateTimeField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_alerts', to=settings.AUTH_USER_MODEL)),
                ('target_users', models.ManyToManyField(blank=True, related_name='system_alerts', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'system_alerts',
                'ordering': ['-priority', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SystemMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('metric_type', models.CharField(choices=[('user_count', 'Active Users'), ('patient_count', 'Total Patients'), ('appointment_count', 'Daily Appointments'), ('record_count', 'Medical Records'), ('login_count', 'Daily Logins'), ('api_calls', 'API Calls'), ('response_time', 'Average Response Time'), ('error_rate', 'Error Rate'), ('storage_usage', 'Storage Usage'), ('memory_usage', 'Memory Usage')], max_length=30)),
                ('value', models.FloatField()),
                ('unit', models.CharField(blank=True, max_length=20)),
                ('date', models.DateField()),
                ('hour', models.PositiveIntegerField(blank=True, help_text='Hour of day (0-23)', null=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'system_metrics',
                'ordering': ['-date', '-hour'],
                'unique_together': {('metric_type', 'date', 'hour')},
            },
        ),
        migrations.CreateModel(
            name='DashboardWidget',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('widget_type', models.CharField(choices=[('patient_stats', 'Patient Statistics'), ('recent_patients', 'Recent Patients'), ('appointments', 'Upcoming Appointments'), ('medical_alerts', 'Medical Alerts'), ('treatment_progress', 'Treatment Progress'), ('vital_signs', 'Vital Signs Monitor'), ('lab_results', 'Lab Results'), ('imaging_studies', 'Imaging Studies'), ('medication_reminders', 'Medication Reminders'), ('system_status', 'System Status')], max_length=30)),
                ('title', models.CharField(max_length=100)),
                ('position_x', models.PositiveIntegerField(default=0)),
                ('position_y', models.PositiveIntegerField(default=0)),
                ('width', models.PositiveIntegerField(default=1)),
                ('height', models.PositiveIntegerField(default=1)),
                ('is_visible', models.BooleanField(default=True)),
                ('configuration', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dashboard_widgets', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'dashboard_widgets',
                'ordering': ['position_y', 'position_x'],
                'unique_together': {('user', 'widget_type')},
            },
        ),
        migrations.CreateModel(
            name='UserActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('activity_type', models.CharField(choices=[('login', 'User Login'), ('logout', 'User Logout'), ('patient_view', 'Patient Viewed'), ('patient_create', 'Patient Created'), ('patient_update', 'Patient Updated'), ('record_create', 'Medical Record Created'), ('record_update', 'Medical Record Updated'), ('appointment_create', 'Appointment Created'), ('appointment_update', 'Appointment Updated'), ('report_generate', 'Report Generated'), ('model_view', '3D Model Viewed'), ('diagnosis_create', 'Diagnosis Created'), ('treatment_update', 'Treatment Updated')], max_length=30)),
                ('description', models.CharField(max_length=500)),
                ('patient_id', models.CharField(blank=True, max_length=20, null=True)),
                ('object_id', models.PositiveIntegerField(blank=True, null=True)),
                ('object_type', models.CharField(blank=True, max_length=50)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('additional_data', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activities', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'user_activities',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'activity_type'], name='user_activi_user_id_290628_idx'), models.Index(fields=['created_at'], name='user_activi_created_9fa3ca_idx')],
            },
        ),
    ]
