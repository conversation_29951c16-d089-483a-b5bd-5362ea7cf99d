# Quant-NEX Unified Server Startup Script for Windows
# Runs Django backend and Next.js frontend in parallel

Write-Host "🎯 QUANT-NEX UNIFIED SERVER STARTUP" -ForegroundColor Cyan
Write-Host "=" * 50 -ForegroundColor Cyan
Write-Host "Starting Django Backend and Next.js Frontend in parallel..." -ForegroundColor Yellow
Write-Host "Single-page hosting with API proxy enabled" -ForegroundColor Yellow
Write-Host ""

# Function to test if a port is available
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    } catch {
        return $false
    }
}

# Function to wait for server to be ready
function Wait-ForServer {
    param([string]$Url, [string]$Name, [int]$TimeoutSeconds = 30)
    
    Write-Host "⏳ Waiting for $Name to start..." -ForegroundColor Yellow
    
    for ($i = 0; $i -lt $TimeoutSeconds; $i++) {
        try {
            $response = Invoke-WebRequest -Uri $Url -TimeoutSec 2 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Host "✅ $Name: READY" -ForegroundColor Green
                return $true
            }
        } catch {
            # Server not ready yet
        }
        Start-Sleep -Seconds 1
    }
    
    Write-Host "❌ $Name: TIMEOUT" -ForegroundColor Red
    return $false
}

# Start Django Backend
Write-Host "🚀 Starting Django Backend Server..." -ForegroundColor Green
$djangoJob = Start-Job -ScriptBlock {
    Set-Location "C:\Users\<USER>\Downloads\quant-nex\backend"
    python manage.py runserver localhost:8000
}

# Start Next.js Frontend
Write-Host "🌐 Starting Next.js Frontend Server..." -ForegroundColor Green
$nextjsJob = Start-Job -ScriptBlock {
    Set-Location "C:\Users\<USER>\Downloads\quant-nex"
    npm run dev
}

# Wait for both servers to be ready
$djangoReady = Wait-ForServer -Url "http://localhost:8000/api/docs/" -Name "Django Backend"
$nextjsReady = Wait-ForServer -Url "http://localhost:3000" -Name "Next.js Frontend"

if ($djangoReady -and $nextjsReady) {
    # Test integration
    Write-Host ""
    Write-Host "🔧 Testing Integration..." -ForegroundColor Yellow
    
    try {
        $loginTest = Invoke-RestMethod -Uri "http://localhost:3000/api/auth/login/" -Method Post -Body (@{
            username = "admin"
            password = "quantnex123"
        } | ConvertTo-Json) -ContentType "application/json"
        
        Write-Host "✅ API Proxy: WORKING" -ForegroundColor Green
        Write-Host "✅ Authentication: WORKING" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ Integration test failed, but servers are running" -ForegroundColor Yellow
    }
    
    Write-Host ""
    Write-Host "=" * 50 -ForegroundColor Cyan
    Write-Host "🎉 QUANT-NEX UNIFIED SERVER: READY!" -ForegroundColor Green
    Write-Host "=" * 50 -ForegroundColor Cyan
    Write-Host ""
    Write-Host "📍 ACCESS POINTS:" -ForegroundColor White
    Write-Host "   🌐 Main Application: http://localhost:3000" -ForegroundColor Cyan
    Write-Host "   🔐 Login Page:       http://localhost:3000/login" -ForegroundColor Cyan
    Write-Host "   📊 Dashboard:        http://localhost:3000/dashboard" -ForegroundColor Cyan
    Write-Host "   🔧 API Docs:         http://localhost:3000/api/docs/" -ForegroundColor Cyan
    Write-Host "   ⚙️  Admin Panel:      http://localhost:3000/admin/" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "🔑 TEST CREDENTIALS:" -ForegroundColor White
    Write-Host "   Username: admin" -ForegroundColor Yellow
    Write-Host "   Password: quantnex123" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "✨ FEATURES AVAILABLE:" -ForegroundColor White
    Write-Host "   • Single-page hosting (all on localhost:3000)" -ForegroundColor Green
    Write-Host "   • API proxy (backend accessible through frontend)" -ForegroundColor Green
    Write-Host "   • 3D medical visualizations" -ForegroundColor Green
    Write-Host "   • Real-time dashboard" -ForegroundColor Green
    Write-Host "   • Secure authentication" -ForegroundColor Green
    Write-Host "   • Complete healthcare management" -ForegroundColor Green
    Write-Host ""
    Write-Host "🌐 Opening application in browser..." -ForegroundColor Yellow
    Start-Process "http://localhost:3000"
    
    Write-Host ""
    Write-Host "Press Ctrl+C to stop all servers..." -ForegroundColor Yellow
    
    # Keep script running and monitor jobs
    try {
        while ($true) {
            # Check if jobs are still running
            if ($djangoJob.State -ne "Running" -or $nextjsJob.State -ne "Running") {
                Write-Host "⚠️ One or more servers stopped unexpectedly" -ForegroundColor Red
                break
            }
            Start-Sleep -Seconds 2
        }
    } catch {
        Write-Host "🛑 Shutting down servers..." -ForegroundColor Red
    }
} else {
    Write-Host "❌ Server startup failed!" -ForegroundColor Red
}

# Cleanup
Write-Host "🧹 Cleaning up..." -ForegroundColor Yellow
Stop-Job $djangoJob -ErrorAction SilentlyContinue
Stop-Job $nextjsJob -ErrorAction SilentlyContinue
Remove-Job $djangoJob -ErrorAction SilentlyContinue
Remove-Job $nextjsJob -ErrorAction SilentlyContinue

Write-Host "✅ Cleanup complete" -ForegroundColor Green
