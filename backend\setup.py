#!/usr/bin/env python3
"""
Setup script for Quant-NEX Healthcare Application Backend
"""

import os
import sys
import subprocess
import django
from django.core.management import execute_from_command_line

def setup_environment():
    """Set up the Django environment."""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'quantnex.settings')
    django.setup()

def run_command(command):
    """Run a shell command and return the result."""
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {command}")
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"✗ {command}")
        print(f"Error: {e.stderr}")
        return None

def install_dependencies():
    """Install Python dependencies."""
    print("Installing Python dependencies...")
    return run_command("pip install -r requirements.txt")

def create_migrations():
    """Create Django migrations."""
    print("Creating database migrations...")
    setup_environment()
    execute_from_command_line(['manage.py', 'makemigrations'])

def run_migrations():
    """Run Django migrations."""
    print("Running database migrations...")
    setup_environment()
    execute_from_command_line(['manage.py', 'migrate'])

def create_superuser():
    """Create Django superuser."""
    print("Creating superuser...")
    print("Please enter superuser details:")
    setup_environment()
    execute_from_command_line(['manage.py', 'createsuperuser'])

def collect_static():
    """Collect static files."""
    print("Collecting static files...")
    setup_environment()
    execute_from_command_line(['manage.py', 'collectstatic', '--noinput'])

def create_sample_data():
    """Create sample data for development."""
    print("Creating sample data...")
    setup_environment()
    
    from django.contrib.auth import get_user_model
    from patients.models import Patient
    from authentication.models import UserPreferences
    
    User = get_user_model()
    
    # Create sample doctor
    if not User.objects.filter(username='dr.priya').exists():
        doctor = User.objects.create_user(
            username='dr.priya',
            email='<EMAIL>',
            password='quantnex123',
            first_name='Priya',
            last_name='Sharma',
            role='doctor',
            specialization='oncology',
            license_number='MED-2024-001',
            department='Oncology',
            hospital_name='Apollo Hospital',
            phone_number='+91-**********'
        )
        UserPreferences.objects.create(user=doctor)
        print("✓ Created sample doctor: dr.priya")
    
    # Create sample patients
    sample_patients = [
        {
            'first_name': 'Arjun',
            'last_name': 'Patel',
            'date_of_birth': '1967-03-15',
            'gender': 'M',
            'phone_number': '+91-**********',
            'address': '123 MG Road, Mumbai',
            'city': 'Mumbai',
            'state': 'Maharashtra',
            'postal_code': '400001',
            'emergency_contact_name': 'Sunita Patel',
            'emergency_contact_phone': '+91-**********',
            'emergency_contact_relation': 'Wife',
            'cancer_type': 'Lung Carcinoma',
            'cancer_stage': 'III',
            'status': 'active',
            'treatment_progress': 42,
        },
        {
            'first_name': 'Kavya',
            'last_name': 'Singh',
            'date_of_birth': '1985-07-22',
            'gender': 'F',
            'phone_number': '+91-**********',
            'address': '456 Park Street, Delhi',
            'city': 'Delhi',
            'state': 'Delhi',
            'postal_code': '110001',
            'emergency_contact_name': 'Rajesh Singh',
            'emergency_contact_phone': '+91-**********',
            'emergency_contact_relation': 'Husband',
            'cancer_type': 'Breast Cancer',
            'cancer_stage': 'II',
            'status': 'remission',
            'treatment_progress': 100,
        }
    ]
    
    for patient_data in sample_patients:
        if not Patient.objects.filter(
            first_name=patient_data['first_name'],
            last_name=patient_data['last_name']
        ).exists():
            patient_data['assigned_doctor'] = doctor
            Patient.objects.create(**patient_data)
            print(f"✓ Created sample patient: {patient_data['first_name']} {patient_data['last_name']}")

def main():
    """Main setup function."""
    print("🏥 Quant-NEX Healthcare Application Backend Setup")
    print("=" * 50)
    
    # Check if virtual environment is activated
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  Warning: Virtual environment not detected. It's recommended to use a virtual environment.")
        response = input("Continue anyway? (y/N): ")
        if response.lower() != 'y':
            print("Setup cancelled.")
            return
    
    # Install dependencies
    if install_dependencies() is None:
        print("Failed to install dependencies. Please check your pip installation.")
        return
    
    # Create .env file if it doesn't exist
    if not os.path.exists('.env'):
        print("Creating .env file from template...")
        run_command("cp .env.example .env")
        print("⚠️  Please edit .env file with your configuration before proceeding.")
        input("Press Enter when you've configured .env file...")
    
    # Database setup
    create_migrations()
    run_migrations()
    
    # Create superuser
    create_superuser_choice = input("Create superuser? (Y/n): ")
    if create_superuser_choice.lower() != 'n':
        create_superuser()
    
    # Create sample data
    sample_data_choice = input("Create sample data for development? (Y/n): ")
    if sample_data_choice.lower() != 'n':
        create_sample_data()
    
    # Collect static files
    collect_static()
    
    print("\n✅ Setup completed successfully!")
    print("\nNext steps:")
    print("1. Start the development server: python manage.py runserver")
    print("2. Access the API documentation: http://localhost:8000/api/docs/")
    print("3. Access the admin panel: http://localhost:8000/admin/")
    print("\nSample login credentials:")
    print("Username: dr.priya")
    print("Password: quantnex123")

if __name__ == '__main__':
    main()
