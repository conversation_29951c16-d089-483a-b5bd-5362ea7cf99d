"""
Dashboard models for Quant-NEX Healthcare Application.
"""
from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()


class DashboardWidget(models.Model):
    """
    Customizable dashboard widgets for users.
    """
    
    WIDGET_TYPES = [
        ('patient_stats', 'Patient Statistics'),
        ('recent_patients', 'Recent Patients'),
        ('appointments', 'Upcoming Appointments'),
        ('medical_alerts', 'Medical Alerts'),
        ('treatment_progress', 'Treatment Progress'),
        ('vital_signs', 'Vital Signs Monitor'),
        ('lab_results', 'Lab Results'),
        ('imaging_studies', 'Imaging Studies'),
        ('medication_reminders', 'Medication Reminders'),
        ('system_status', 'System Status'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='dashboard_widgets')
    widget_type = models.CharField(max_length=30, choices=WIDGET_TYPES)
    title = models.CharField(max_length=100)
    position_x = models.PositiveIntegerField(default=0)
    position_y = models.PositiveIntegerField(default=0)
    width = models.PositiveIntegerField(default=1)
    height = models.PositiveIntegerField(default=1)
    is_visible = models.BooleanField(default=True)
    configuration = models.JSONField(default=dict, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'dashboard_widgets'
        unique_together = ['user', 'widget_type']
        ordering = ['position_y', 'position_x']
    
    def __str__(self):
        return f"{self.user.display_name} - {self.title}"


class SystemAlert(models.Model):
    """
    System-wide alerts and notifications.
    """
    
    ALERT_TYPES = [
        ('info', 'Information'),
        ('warning', 'Warning'),
        ('error', 'Error'),
        ('critical', 'Critical'),
        ('maintenance', 'Maintenance'),
    ]
    
    PRIORITY_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]
    
    title = models.CharField(max_length=200)
    message = models.TextField()
    alert_type = models.CharField(max_length=20, choices=ALERT_TYPES)
    priority = models.CharField(max_length=20, choices=PRIORITY_LEVELS, default='medium')
    
    # Targeting
    target_users = models.ManyToManyField(User, blank=True, related_name='system_alerts')
    target_roles = models.JSONField(default=list, blank=True, help_text="List of roles to target")
    is_global = models.BooleanField(default=False, help_text="Show to all users")
    
    # Timing
    start_time = models.DateTimeField()
    end_time = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    
    # Metadata
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_alerts')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'system_alerts'
        ordering = ['-priority', '-created_at']
    
    def __str__(self):
        return f"{self.alert_type.upper()}: {self.title}"


class UserActivity(models.Model):
    """
    Track user activities for analytics and audit.
    """
    
    ACTIVITY_TYPES = [
        ('login', 'User Login'),
        ('logout', 'User Logout'),
        ('patient_view', 'Patient Viewed'),
        ('patient_create', 'Patient Created'),
        ('patient_update', 'Patient Updated'),
        ('record_create', 'Medical Record Created'),
        ('record_update', 'Medical Record Updated'),
        ('appointment_create', 'Appointment Created'),
        ('appointment_update', 'Appointment Updated'),
        ('report_generate', 'Report Generated'),
        ('model_view', '3D Model Viewed'),
        ('diagnosis_create', 'Diagnosis Created'),
        ('treatment_update', 'Treatment Updated'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='activities')
    activity_type = models.CharField(max_length=30, choices=ACTIVITY_TYPES)
    description = models.CharField(max_length=500)
    
    # Related objects
    patient_id = models.CharField(max_length=20, blank=True, null=True)
    object_id = models.PositiveIntegerField(blank=True, null=True)
    object_type = models.CharField(max_length=50, blank=True)
    
    # Metadata
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    user_agent = models.TextField(blank=True)
    additional_data = models.JSONField(default=dict, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'user_activities'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'activity_type']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.user.display_name} - {self.activity_type} ({self.created_at})"


class SystemMetrics(models.Model):
    """
    Store system performance and usage metrics.
    """
    
    METRIC_TYPES = [
        ('user_count', 'Active Users'),
        ('patient_count', 'Total Patients'),
        ('appointment_count', 'Daily Appointments'),
        ('record_count', 'Medical Records'),
        ('login_count', 'Daily Logins'),
        ('api_calls', 'API Calls'),
        ('response_time', 'Average Response Time'),
        ('error_rate', 'Error Rate'),
        ('storage_usage', 'Storage Usage'),
        ('memory_usage', 'Memory Usage'),
    ]
    
    metric_type = models.CharField(max_length=30, choices=METRIC_TYPES)
    value = models.FloatField()
    unit = models.CharField(max_length=20, blank=True)
    date = models.DateField()
    hour = models.PositiveIntegerField(null=True, blank=True, help_text="Hour of day (0-23)")
    
    metadata = models.JSONField(default=dict, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'system_metrics'
        unique_together = ['metric_type', 'date', 'hour']
        ordering = ['-date', '-hour']
    
    def __str__(self):
        return f"{self.metric_type}: {self.value} {self.unit} ({self.date})"
