#!/usr/bin/env python3
"""
FINAL INTEGRATION VERIFICATION for Quant-NEX
Complete end-to-end testing of the full-stack application
"""

import requests
import json
import time
from datetime import datetime

def print_header(title):
    """Print a formatted header"""
    print(f"\n{'='*60}")
    print(f"🏥 {title}")
    print(f"{'='*60}")

def print_section(title):
    """Print a formatted section"""
    print(f"\n🔍 {title}")
    print("-" * 40)

def test_servers_running():
    """Verify both servers are running"""
    print_section("Server Status Check")
    
    # Test Django backend
    try:
        response = requests.get("http://localhost:8000/api/docs/", timeout=3)
        if response.status_code == 200:
            print("✅ Django Backend: RUNNING (Port 8000)")
        else:
            print(f"⚠️ Django Backend: Unexpected status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Django Backend: NOT RUNNING - {e}")
        return False
    
    # Test Next.js frontend
    try:
        response = requests.get("http://localhost:3000", timeout=3)
        if response.status_code == 200:
            print("✅ Next.js Frontend: RUNNING (Port 3000)")
        else:
            print(f"⚠️ Next.js Frontend: Unexpected status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Next.js Frontend: NOT RUNNING - {e}")
        return False
    
    return True

def test_complete_auth_flow():
    """Test complete authentication flow"""
    print_section("Authentication Flow Test")
    
    # Step 1: Login
    login_data = {
        "username": "admin",
        "password": "quantnex123"
    }
    
    try:
        response = requests.post("http://localhost:8000/api/auth/login/", json=login_data)
        if response.status_code == 200:
            data = response.json()
            print("✅ Login: SUCCESS")
            print(f"   User: {data['user']['display_name']}")
            print(f"   Role: {data['user']['role']}")
            print(f"   Email: {data['user']['email']}")
            
            # Step 2: Test protected endpoint
            headers = {"Authorization": f"Bearer {data['access']}"}
            profile_response = requests.get("http://localhost:8000/api/auth/profile/", headers=headers)
            
            if profile_response.status_code == 200:
                print("✅ Protected Endpoints: ACCESSIBLE")
                return data['access']
            else:
                print(f"❌ Protected Endpoints: FAILED ({profile_response.status_code})")
                return None
        else:
            print(f"❌ Login: FAILED ({response.status_code})")
            return None
    except Exception as e:
        print(f"❌ Authentication Error: {e}")
        return None

def test_api_endpoints(token):
    """Test all major API endpoints"""
    print_section("API Endpoints Test")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    endpoints = [
        ("Dashboard Overview", "/dashboard/overview/"),
        ("Patient Statistics", "/dashboard/patient-stats/"),
        ("System Status", "/dashboard/system-status/"),
        ("User Profile", "/auth/profile/"),
        ("Patients List", "/patients/"),
    ]
    
    working_count = 0
    for name, endpoint in endpoints:
        try:
            response = requests.get(f"http://localhost:8000/api{endpoint}", headers=headers)
            if response.status_code == 200:
                print(f"✅ {name}: WORKING")
                working_count += 1
            elif response.status_code == 404:
                print(f"⚠️ {name}: NOT IMPLEMENTED (404)")
            else:
                print(f"⚠️ {name}: STATUS {response.status_code}")
        except Exception as e:
            print(f"❌ {name}: ERROR - {e}")
    
    print(f"\nAPI Endpoints Summary: {working_count}/{len(endpoints)} working")
    return working_count >= 3  # At least 3 endpoints should work

def test_frontend_pages():
    """Test frontend page accessibility"""
    print_section("Frontend Pages Test")
    
    pages = [
        ("Landing Page", "/"),
        ("Login Page", "/login"),
        ("Dashboard", "/dashboard"),
        ("Patients", "/patients"),
        ("Diagnosis", "/diagnosis"),
        ("Treatment", "/treatment"),
        ("Reports", "/reports"),
        ("Monitoring", "/monitoring"),
    ]
    
    accessible_count = 0
    for name, path in pages:
        try:
            response = requests.get(f"http://localhost:3000{path}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {name}: ACCESSIBLE")
                accessible_count += 1
            else:
                print(f"⚠️ {name}: STATUS {response.status_code}")
        except Exception as e:
            print(f"❌ {name}: ERROR - {e}")
    
    print(f"\nFrontend Pages Summary: {accessible_count}/{len(pages)} accessible")
    return accessible_count >= 6  # At least 6 pages should be accessible

def test_integration_features():
    """Test integration-specific features"""
    print_section("Integration Features Test")
    
    # Test CORS
    try:
        headers = {
            'Origin': 'http://localhost:3000',
            'Access-Control-Request-Method': 'POST',
            'Access-Control-Request-Headers': 'Content-Type,Authorization'
        }
        response = requests.options("http://localhost:8000/api/auth/login/", headers=headers)
        if response.status_code in [200, 204] and 'Access-Control-Allow-Origin' in response.headers:
            print("✅ CORS Configuration: WORKING")
            cors_ok = True
        else:
            print("❌ CORS Configuration: FAILED")
            cors_ok = False
    except Exception as e:
        print(f"❌ CORS Test Error: {e}")
        cors_ok = False
    
    # Test API Documentation
    try:
        response = requests.get("http://localhost:8000/api/docs/")
        if response.status_code == 200:
            print("✅ API Documentation: ACCESSIBLE")
            docs_ok = True
        else:
            print("❌ API Documentation: FAILED")
            docs_ok = False
    except Exception as e:
        print(f"❌ API Documentation Error: {e}")
        docs_ok = False
    
    return cors_ok and docs_ok

def main():
    """Run complete integration verification"""
    print_header("QUANT-NEX FINAL INTEGRATION VERIFICATION")
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test 1: Server Status
    servers_ok = test_servers_running()
    
    # Test 2: Authentication
    token = test_complete_auth_flow() if servers_ok else None
    auth_ok = token is not None
    
    # Test 3: API Endpoints
    api_ok = test_api_endpoints(token) if token else False
    
    # Test 4: Frontend Pages
    frontend_ok = test_frontend_pages() if servers_ok else False
    
    # Test 5: Integration Features
    integration_ok = test_integration_features() if servers_ok else False
    
    # Final Results
    print_header("FINAL VERIFICATION RESULTS")
    
    tests = [
        ("Server Status", servers_ok),
        ("Authentication Flow", auth_ok),
        ("API Endpoints", api_ok),
        ("Frontend Pages", frontend_ok),
        ("Integration Features", integration_ok),
    ]
    
    passed_tests = sum(1 for _, result in tests if result)
    
    for test_name, result in tests:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<20}: {status}")
    
    print(f"\nOVERALL SCORE: {passed_tests}/{len(tests)} ({passed_tests/len(tests)*100:.0f}%)")
    
    if passed_tests == len(tests):
        print("\n🎉 PERFECT SCORE! Full-stack integration is COMPLETE!")
        print("\n🚀 READY FOR PRODUCTION:")
        print("   • All servers running correctly")
        print("   • Authentication system working")
        print("   • API endpoints responding")
        print("   • Frontend pages accessible")
        print("   • Integration features operational")
        
        print("\n🌐 ACCESS POINTS:")
        print("   • Application: http://localhost:3000")
        print("   • Login: http://localhost:3000/login")
        print("   • API Docs: http://localhost:8000/api/docs/")
        print("   • Admin: http://localhost:8000/admin/")
        
        print("\n🔑 CREDENTIALS:")
        print("   • Username: admin")
        print("   • Password: quantnex123")
        
        print("\n✨ USER JOURNEY:")
        print("   1. Visit http://localhost:3000")
        print("   2. Click 'Get Started' → Login")
        print("   3. Use credentials to authenticate")
        print("   4. Explore dashboard and modules")
        print("   5. Test 3D visualizations and features")
        
        return 0
    else:
        print(f"\n⚠️ {len(tests)-passed_tests} test(s) failed. Review issues above.")
        return 1

if __name__ == '__main__':
    exit(main())
