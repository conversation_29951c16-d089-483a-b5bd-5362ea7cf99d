"""
Patient URLs for Quant-NEX Healthcare Application.
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# Create router for viewsets
router = DefaultRouter()
router.register(r'', views.PatientViewSet, basename='patient')

urlpatterns = [
    # Patient CRUD operations
    path('', include(router.urls)),
    
    # Patient statistics and analytics
    path('stats/', views.patient_statistics, name='patient-stats'),
    path('search/', views.patient_search, name='patient-search'),
    
    # Medical records
    path('<int:patient_id>/records/', views.MedicalRecordListCreateView.as_view(), name='patient-records'),
    path('records/<int:pk>/', views.MedicalRecordDetailView.as_view(), name='medical-record-detail'),
    
    # Appointments
    path('<int:patient_id>/appointments/', views.PatientAppointmentListView.as_view(), name='patient-appointments'),
    path('appointments/', views.AppointmentListCreateView.as_view(), name='appointment-list'),
    path('appointments/<int:pk>/', views.AppointmentDetailView.as_view(), name='appointment-detail'),
    
    # Vital signs
    path('<int:patient_id>/vitals/', views.patient_vital_signs, name='patient-vitals'),
]
