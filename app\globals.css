@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* High-contrast black and white theme */
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;

    --card: 0 0% 0%;
    --card-foreground: 0 0% 100%;

    --popover: 0 0% 0%;
    --popover-foreground: 0 0% 100%;

    /* Primary blue glow color */
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 100%;

    /* Secondary with blue accent */
    --secondary: 0 0% 5%;
    --secondary-foreground: 0 0% 100%;

    --muted: 0 0% 10%;
    --muted-foreground: 0 0% 70%;

    /* Blue accent for interactive elements */
    --accent: 217 91% 60%;
    --accent-foreground: 0 0% 100%;

    /* High-contrast destructive */
    --destructive: 0 100% 50%;
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 20%;
    --input: 0 0% 5%;
    --ring: 217 91% 60%;

    --radius: 0.75rem;

    /* High-contrast theme colors with blue accents */
    --medical-blue: 217 91% 60%;
    --medical-blue-glow: 217 91% 60%;
    --medical-secondary: 0 0% 5%;
    --medical-accent: 217 91% 70%;
    --medical-border: 0 0% 20%;
    --medical-text: 0 0% 100%;
    --medical-background: 0 0% 0%;
    --medical-surface: 0 0% 3%;
  }

  .dark {
    /* High-contrast black and white theme (same as light mode for consistency) */
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;

    --card: 0 0% 0%;
    --card-foreground: 0 0% 100%;

    --popover: 0 0% 0%;
    --popover-foreground: 0 0% 100%;

    /* Primary blue glow color */
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 100%;

    /* Secondary with blue accent */
    --secondary: 0 0% 5%;
    --secondary-foreground: 0 0% 100%;

    --muted: 0 0% 10%;
    --muted-foreground: 0 0% 70%;

    /* Blue accent for interactive elements */
    --accent: 217 91% 60%;
    --accent-foreground: 0 0% 100%;

    /* High-contrast destructive */
    --destructive: 0 100% 50%;
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 20%;
    --input: 0 0% 5%;
    --ring: 217 91% 60%;

    /* High-contrast theme colors with blue accents */
    --medical-blue: 217 91% 60%;
    --medical-blue-glow: 217 91% 60%;
    --medical-secondary: 0 0% 5%;
    --medical-accent: 217 91% 70%;
    --medical-border: 0 0% 20%;
    --medical-text: 0 0% 100%;
    --medical-background: 0 0% 0%;
    --medical-surface: 0 0% 3%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }
}

@layer components {
  /* High-contrast component styles with blue glow effects */
  .medical-card {
    @apply bg-black border border-medical-border rounded-xl transition-all duration-300;
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.1);
  }

  .medical-card:hover {
    @apply border-medical-blue;
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3), 0 0 40px rgba(59, 130, 246, 0.1);
    transform: translateY(-2px);
  }

  .medical-button {
    @apply bg-black border-2 border-medical-blue text-white font-medium px-6 py-3 rounded-lg transition-all duration-200;
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
  }

  .medical-button:hover {
    @apply bg-medical-blue;
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6), 0 0 40px rgba(59, 130, 246, 0.3);
    transform: translateY(-1px);
  }

  .medical-button-secondary {
    @apply bg-black border border-medical-border text-white font-medium px-6 py-3 rounded-lg transition-all duration-200;
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.2);
  }

  .medical-button-secondary:hover {
    @apply border-medical-blue;
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);
  }

  /* Blue glow effect classes */
  .glow-blue {
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.5), 0 0 20px rgba(59, 130, 246, 0.3), 0 0 30px rgba(59, 130, 246, 0.1);
  }

  .glow-blue-intense {
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.7), 0 0 30px rgba(59, 130, 246, 0.5), 0 0 45px rgba(59, 130, 246, 0.3);
  }

  .glow-blue-subtle {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3), 0 0 10px rgba(59, 130, 246, 0.2);
  }

  .glow-blue-animated {
    animation: blueGlow 2s ease-in-out infinite alternate;
  }

  @keyframes blueGlow {
    from {
      box-shadow: 0 0 5px rgba(59, 130, 246, 0.3), 0 0 10px rgba(59, 130, 246, 0.2);
    }
    to {
      box-shadow: 0 0 15px rgba(59, 130, 246, 0.6), 0 0 25px rgba(59, 130, 246, 0.4), 0 0 35px rgba(59, 130, 246, 0.2);
    }
  }

  /* High-contrast input styles */
  .medical-input {
    @apply bg-black border-2 border-medical-border text-white rounded-lg px-4 py-2 transition-all duration-200;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.5);
  }

  .medical-input:focus {
    @apply border-medical-blue outline-none;
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.5), inset 0 0 5px rgba(59, 130, 246, 0.1);
  }

  /* High-contrast surface */
  .medical-surface {
    @apply bg-medical-surface border border-medical-border;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
  }

  .medical-input {
    @apply border-2 border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 rounded-lg px-4 py-3 transition-all duration-200;
  }

  .status-indicator {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium;
  }

  .status-critical {
    @apply bg-red-100 text-red-800 border border-red-200;
  }

  .status-warning {
    @apply bg-yellow-100 text-yellow-800 border border-yellow-200;
  }

  .status-normal {
    @apply bg-green-100 text-green-800 border border-green-200;
  }

  .status-info {
    @apply bg-blue-100 text-blue-800 border border-blue-200;
  }

  .pulse-animation {
    animation: pulse-glow 2s ease-in-out infinite alternate;
  }

  .heartbeat-animation {
    animation: heartbeat 1.5s ease-in-out infinite;
  }
}

@layer utilities {
  /* High-contrast utility classes */
  .text-medical-blue {
    color: hsl(var(--medical-blue));
  }

  .text-medical-text {
    color: hsl(var(--medical-text));
  }

  .bg-medical-blue {
    background-color: hsl(var(--medical-blue));
  }

  .bg-medical-background {
    background-color: hsl(var(--medical-background));
  }

  .bg-medical-surface {
    background-color: hsl(var(--medical-surface));
  }

  .bg-medical-secondary {
    background-color: hsl(var(--medical-secondary));
  }

  .border-medical-blue {
    border-color: hsl(var(--medical-blue));
  }

  .border-medical-border {
    border-color: hsl(var(--medical-border));
  }

  /* Blue glow utilities */
  .shadow-glow-blue {
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.5), 0 0 20px rgba(59, 130, 246, 0.3);
  }

  .shadow-glow-blue-intense {
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.7), 0 0 30px rgba(59, 130, 246, 0.5), 0 0 45px rgba(59, 130, 246, 0.3);
  }

  .shadow-glow-blue-subtle {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3), 0 0 10px rgba(59, 130, 246, 0.2);
  }

  /* High-contrast gradients */
  .gradient-medical {
    background: linear-gradient(135deg, #000000 0%, hsl(var(--medical-blue)) 50%, #000000 100%);
  }

  .gradient-medical-surface {
    background: linear-gradient(135deg, hsl(var(--medical-background)) 0%, hsl(var(--medical-surface)) 100%);
  }

  /* Focus states with blue glow */
  .focus-glow:focus {
    outline: none;
    box-shadow: 0 0 0 2px hsl(var(--medical-blue)), 0 0 10px rgba(59, 130, 246, 0.5);
  }

  /* Hover glow effects */
  .hover-glow:hover {
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.6), 0 0 25px rgba(59, 130, 246, 0.4);
    transform: translateY(-1px);
    transition: all 0.2s ease-in-out;
  }
}

  .gradient-medical-green {
    background: linear-gradient(135deg, hsl(var(--medical-blue)) 0%, hsl(var(--medical-blue)) 100%);
  }

@keyframes pulse-glow {
  0% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.4);
  }
}

@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Custom styles for Quant-NEX */
.glow-text {
  text-shadow: 0 0 10px rgba(139, 92, 246, 0.5), 0 0 20px rgba(139, 92, 246, 0.3);
}

.glow-border {
  box-shadow: 0 0 10px rgba(139, 92, 246, 0.5), 0 0 20px rgba(139, 92, 246, 0.3);
}

.neon-glow {
  filter: drop-shadow(0 0 8px rgba(139, 92, 246, 0.8));
}

/* Gradient animations */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 5s ease infinite;
}
