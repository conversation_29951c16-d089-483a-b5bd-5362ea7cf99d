@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Healthcare-focused primary colors - Medical Blue */
    --primary: 200 100% 45%;
    --primary-foreground: 210 40% 98%;

    /* Healthcare secondary - Soft Green for health/wellness */
    --secondary: 142 76% 36%;
    --secondary-foreground: 210 40% 98%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    /* Healthcare accent - Warm teal for medical equipment */
    --accent: 180 100% 35%;
    --accent-foreground: 210 40% 98%;

    /* Medical alert red */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 200 100% 45%;

    --radius: 0.75rem;

    /* Healthcare-specific colors */
    --medical-blue: 200 100% 45%;
    --medical-green: 142 76% 36%;
    --medical-teal: 180 100% 35%;
    --medical-purple: 271 91% 65%;
    --medical-orange: 25 95% 53%;
    --medical-red: 0 84.2% 60.2%;
    --medical-gray: 220 13% 91%;
  }

  .dark {
    --background: 220 27% 8%;
    --foreground: 210 40% 98%;

    --card: 220 27% 12%;
    --card-foreground: 210 40% 98%;

    --popover: 220 27% 12%;
    --popover-foreground: 210 40% 98%;

    /* Healthcare-focused dark mode colors */
    --primary: 200 100% 55%;
    --primary-foreground: 220 27% 8%;

    --secondary: 142 76% 46%;
    --secondary-foreground: 220 27% 8%;

    --muted: 220 27% 16%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 180 100% 45%;
    --accent-foreground: 220 27% 8%;

    --destructive: 0 62.8% 50.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 27% 20%;
    --input: 220 27% 20%;
    --ring: 200 100% 55%;

    /* Healthcare-specific dark colors */
    --medical-blue: 200 100% 55%;
    --medical-green: 142 76% 46%;
    --medical-teal: 180 100% 45%;
    --medical-purple: 271 91% 75%;
    --medical-orange: 25 95% 63%;
    --medical-red: 0 62.8% 50.6%;
    --medical-gray: 220 27% 20%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }
}

@layer components {
  /* Healthcare-specific component styles */
  .medical-card {
    @apply bg-card border border-border rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02];
  }

  .medical-button {
    @apply bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium px-6 py-3 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105;
  }

  .medical-button-secondary {
    @apply bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-medium px-6 py-3 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105;
  }

  .medical-input {
    @apply border-2 border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 rounded-lg px-4 py-3 transition-all duration-200;
  }

  .status-indicator {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium;
  }

  .status-critical {
    @apply bg-red-100 text-red-800 border border-red-200;
  }

  .status-warning {
    @apply bg-yellow-100 text-yellow-800 border border-yellow-200;
  }

  .status-normal {
    @apply bg-green-100 text-green-800 border border-green-200;
  }

  .status-info {
    @apply bg-blue-100 text-blue-800 border border-blue-200;
  }

  .pulse-animation {
    animation: pulse-glow 2s ease-in-out infinite alternate;
  }

  .heartbeat-animation {
    animation: heartbeat 1.5s ease-in-out infinite;
  }
}

@layer utilities {
  .text-medical-blue {
    color: hsl(var(--medical-blue));
  }

  .text-medical-green {
    color: hsl(var(--medical-green));
  }

  .text-medical-teal {
    color: hsl(var(--medical-teal));
  }

  .bg-medical-blue {
    background-color: hsl(var(--medical-blue));
  }

  .bg-medical-green {
    background-color: hsl(var(--medical-green));
  }

  .bg-medical-teal {
    background-color: hsl(var(--medical-teal));
  }

  .border-medical-blue {
    border-color: hsl(var(--medical-blue));
  }

  .shadow-medical {
    box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1), 0 4px 6px -2px rgba(59, 130, 246, 0.05);
  }

  .gradient-medical {
    background: linear-gradient(135deg, hsl(var(--medical-blue)) 0%, hsl(var(--medical-teal)) 100%);
  }

  .gradient-medical-green {
    background: linear-gradient(135deg, hsl(var(--medical-green)) 0%, hsl(var(--medical-teal)) 100%);
  }
}

@keyframes pulse-glow {
  0% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.4);
  }
}

@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Custom styles for Quant-NEX */
.glow-text {
  text-shadow: 0 0 10px rgba(139, 92, 246, 0.5), 0 0 20px rgba(139, 92, 246, 0.3);
}

.glow-border {
  box-shadow: 0 0 10px rgba(139, 92, 246, 0.5), 0 0 20px rgba(139, 92, 246, 0.3);
}

.neon-glow {
  filter: drop-shadow(0 0 8px rgba(139, 92, 246, 0.8));
}

/* Gradient animations */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 5s ease infinite;
}
