"""
Patient serializers for Quant-NEX Healthcare Application.
"""
from rest_framework import serializers
from .models import Patient, MedicalR<PERSON>ord, Appointment
from authentication.serializers import UserListSerializer


class PatientSerializer(serializers.ModelSerializer):
    """
    Comprehensive patient serializer.
    """
    full_name = serializers.ReadOnlyField()
    age = serializers.ReadOnlyField()
    bmi = serializers.ReadOnlyField()
    assigned_doctor_name = serializers.CharField(
        source='assigned_doctor.display_name', 
        read_only=True
    )
    created_by_name = serializers.CharField(
        source='created_by.display_name', 
        read_only=True
    )
    
    class Meta:
        model = Patient
        fields = [
            'id', 'patient_id', 'first_name', 'last_name', 'middle_name',
            'full_name', 'date_of_birth', 'age', 'gender', 'blood_group',
            'phone_number', 'email', 'address', 'city', 'state',
            'postal_code', 'country', 'emergency_contact_name',
            'emergency_contact_phone', 'emergency_contact_relation',
            'height', 'weight', 'bmi', 'allergies', 'medical_history',
            'current_medications', 'cancer_type', 'cancer_stage',
            'diagnosis_date', 'status', 'treatment_progress',
            'assigned_doctor', 'assigned_doctor_name', 'created_by',
            'created_by_name', 'created_at', 'updated_at', 'last_visit'
        ]
        read_only_fields = [
            'id', 'patient_id', 'created_by', 'created_at', 'updated_at'
        ]
    
    def create(self, validated_data):
        # Set created_by to current user
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class PatientListSerializer(serializers.ModelSerializer):
    """
    Simplified patient serializer for list views.
    """
    full_name = serializers.ReadOnlyField()
    age = serializers.ReadOnlyField()
    assigned_doctor_name = serializers.CharField(
        source='assigned_doctor.display_name', 
        read_only=True
    )
    
    class Meta:
        model = Patient
        fields = [
            'id', 'patient_id', 'full_name', 'age', 'gender',
            'cancer_type', 'status', 'treatment_progress',
            'assigned_doctor_name', 'last_visit', 'created_at'
        ]


class MedicalRecordSerializer(serializers.ModelSerializer):
    """
    Medical record serializer.
    """
    doctor_name = serializers.CharField(
        source='doctor.display_name', 
        read_only=True
    )
    patient_name = serializers.CharField(
        source='patient.full_name', 
        read_only=True
    )
    
    class Meta:
        model = MedicalRecord
        fields = [
            'id', 'patient', 'patient_name', 'doctor', 'doctor_name',
            'record_type', 'title', 'description', 'notes',
            'temperature', 'blood_pressure_systolic', 'blood_pressure_diastolic',
            'heart_rate', 'respiratory_rate', 'oxygen_saturation',
            'attachments', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def create(self, validated_data):
        # Set doctor to current user if not provided
        if not validated_data.get('doctor'):
            validated_data['doctor'] = self.context['request'].user
        return super().create(validated_data)


class AppointmentSerializer(serializers.ModelSerializer):
    """
    Appointment serializer.
    """
    patient_name = serializers.CharField(
        source='patient.full_name', 
        read_only=True
    )
    doctor_name = serializers.CharField(
        source='doctor.display_name', 
        read_only=True
    )
    
    class Meta:
        model = Appointment
        fields = [
            'id', 'patient', 'patient_name', 'doctor', 'doctor_name',
            'appointment_type', 'title', 'description', 'scheduled_date',
            'scheduled_time', 'duration', 'status', 'notes',
            'send_reminder', 'reminder_sent', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'reminder_sent', 'created_at', 'updated_at']


class PatientStatsSerializer(serializers.Serializer):
    """
    Patient statistics serializer.
    """
    total_patients = serializers.IntegerField()
    active_patients = serializers.IntegerField()
    critical_patients = serializers.IntegerField()
    new_patients_this_month = serializers.IntegerField()
    patients_by_status = serializers.DictField()
    patients_by_cancer_type = serializers.DictField()
    average_age = serializers.FloatField()
    gender_distribution = serializers.DictField()


class PatientSearchSerializer(serializers.Serializer):
    """
    Patient search parameters serializer.
    """
    query = serializers.CharField(required=False, allow_blank=True)
    status = serializers.ChoiceField(
        choices=Patient.STATUS_CHOICES, 
        required=False, 
        allow_blank=True
    )
    cancer_type = serializers.CharField(required=False, allow_blank=True)
    assigned_doctor = serializers.IntegerField(required=False)
    age_min = serializers.IntegerField(required=False, min_value=0)
    age_max = serializers.IntegerField(required=False, min_value=0)
    gender = serializers.ChoiceField(
        choices=Patient.GENDER_CHOICES, 
        required=False, 
        allow_blank=True
    )


class VitalSignsSerializer(serializers.Serializer):
    """
    Vital signs data serializer.
    """
    temperature = serializers.FloatField(required=False)
    blood_pressure_systolic = serializers.IntegerField(required=False)
    blood_pressure_diastolic = serializers.IntegerField(required=False)
    heart_rate = serializers.IntegerField(required=False)
    respiratory_rate = serializers.IntegerField(required=False)
    oxygen_saturation = serializers.FloatField(required=False)
    recorded_at = serializers.DateTimeField(read_only=True)
