"""
Patient admin configuration for Quant-NEX Healthcare Application.
"""
from django.contrib import admin
from .models import Patient, MedicalRecord, Appointment


@admin.register(Patient)
class PatientAdmin(admin.ModelAdmin):
    """
    Patient admin with comprehensive fields.
    """
    list_display = [
        'patient_id', 'full_name', 'age', 'gender', 'cancer_type',
        'status', 'assigned_doctor', 'created_at'
    ]
    list_filter = [
        'status', 'gender', 'cancer_type', 'assigned_doctor',
        'created_at', 'blood_group'
    ]
    search_fields = [
        'patient_id', 'first_name', 'last_name', 'phone_number',
        'email', 'cancer_type'
    ]
    readonly_fields = ['patient_id', 'age', 'bmi', 'created_at', 'updated_at']
    ordering = ['-created_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': (
                'patient_id', 'first_name', 'middle_name', 'last_name',
                'date_of_birth', 'age', 'gender', 'blood_group'
            )
        }),
        ('Contact Information', {
            'fields': (
                'phone_number', 'email', 'address', 'city', 'state',
                'postal_code', 'country'
            )
        }),
        ('Emergency Contact', {
            'fields': (
                'emergency_contact_name', 'emergency_contact_phone',
                'emergency_contact_relation'
            )
        }),
        ('Medical Information', {
            'fields': (
                'height', 'weight', 'bmi', 'allergies', 'medical_history',
                'current_medications'
            )
        }),
        ('Cancer Information', {
            'fields': (
                'cancer_type', 'cancer_stage', 'diagnosis_date',
                'status', 'treatment_progress'
            )
        }),
        ('System Information', {
            'fields': (
                'assigned_doctor', 'created_by', 'created_at',
                'updated_at', 'last_visit'
            )
        }),
    )


@admin.register(MedicalRecord)
class MedicalRecordAdmin(admin.ModelAdmin):
    """
    Medical record admin.
    """
    list_display = [
        'patient', 'record_type', 'title', 'doctor', 'created_at'
    ]
    list_filter = ['record_type', 'doctor', 'created_at']
    search_fields = ['patient__patient_id', 'title', 'description']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['-created_at']


@admin.register(Appointment)
class AppointmentAdmin(admin.ModelAdmin):
    """
    Appointment admin.
    """
    list_display = [
        'patient', 'doctor', 'appointment_type', 'scheduled_date',
        'scheduled_time', 'status'
    ]
    list_filter = [
        'appointment_type', 'status', 'scheduled_date', 'doctor'
    ]
    search_fields = ['patient__patient_id', 'title', 'description']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['scheduled_date', 'scheduled_time']
