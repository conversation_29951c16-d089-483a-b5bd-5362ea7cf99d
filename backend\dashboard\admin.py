"""
Dashboard admin configuration for Quant-NEX Healthcare Application.
"""
from django.contrib import admin
from .models import DashboardWidget, SystemAlert, UserActivity, SystemMetrics


@admin.register(DashboardWidget)
class DashboardWidgetAdmin(admin.ModelAdmin):
    """
    Dashboard widget admin.
    """
    list_display = ['user', 'widget_type', 'title', 'is_visible']
    list_filter = ['widget_type', 'is_visible']
    search_fields = ['user__username', 'title']


@admin.register(SystemAlert)
class SystemAlertAdmin(admin.ModelAdmin):
    """
    System alert admin.
    """
    list_display = ['title', 'alert_type', 'priority', 'is_active', 'created_at']
    list_filter = ['alert_type', 'priority', 'is_active']
    search_fields = ['title', 'message']


@admin.register(UserActivity)
class UserActivityAdmin(admin.ModelAdmin):
    """
    User activity admin.
    """
    list_display = ['user', 'activity_type', 'description', 'created_at']
    list_filter = ['activity_type', 'created_at']
    search_fields = ['user__username', 'description']
    readonly_fields = ['created_at']


@admin.register(SystemMetrics)
class SystemMetricsAdmin(admin.ModelAdmin):
    """
    System metrics admin.
    """
    list_display = ['metric_type', 'value', 'unit', 'date', 'hour']
    list_filter = ['metric_type', 'date']
    readonly_fields = ['created_at']
