"""
Authentication URLs for Quant-NEX Healthcare Application.
"""
from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView
from . import views

urlpatterns = [
    # Authentication
    path('register/', views.UserRegistrationView.as_view(), name='user-register'),
    path('login/', views.CustomTokenObtainPairView.as_view(), name='user-login'),
    path('logout/', views.LogoutView.as_view(), name='user-logout'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token-refresh'),
    
    # User Profile
    path('profile/', views.UserProfileView.as_view(), name='user-profile'),
    path('preferences/', views.UserPreferencesView.as_view(), name='user-preferences'),
    path('change-password/', views.PasswordChangeView.as_view(), name='change-password'),
    
    # User Management
    path('users/', views.UserListView.as_view(), name='user-list'),
    path('sessions/', views.UserSessionsView.as_view(), name='user-sessions'),
    path('stats/', views.user_stats, name='user-stats'),
]
