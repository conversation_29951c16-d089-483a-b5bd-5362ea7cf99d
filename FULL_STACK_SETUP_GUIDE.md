# 🏥 Quant-NEX Full-Stack Setup Guide

## Complete Setup Instructions for Next.js Frontend + Django Backend

This guide will help you set up and run both the Next.js frontend and Django backend locally for seamless full-stack development.

---

## 📋 Prerequisites

### Required Software:
- **Node.js 18+** (for Next.js frontend)
- **Python 3.8+** (for Django backend)
- **Git** (for version control)

### Optional but Recommended:
- **PostgreSQL** (for production-like database)
- **Redis** (for caching and background tasks)

---

## 🚀 Quick Start (Recommended)

### Step 1: Setup Django Backend

```bash
# Navigate to backend directory
cd backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Run the automated setup script
python run_backend.py
```

The script will:
- Install all dependencies
- Create database migrations
- Set up environment variables
- Create a superuser account
- Start the development server on http://localhost:8000

### Step 2: Setup Next.js Frontend

```bash
# Open a new terminal and navigate to frontend directory
cd quant-nex  # or your frontend directory name

# Install dependencies
npm install

# Start the development server
npm run dev
```

The frontend will be available at http://localhost:3000

---

## 🔧 Manual Setup (Detailed)

### Backend Setup (Django)

#### 1. Environment Setup
```bash
cd backend
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# Verify Python version
python --version  # Should be 3.8+
```

#### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

#### 3. Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your settings (optional for development)
# The defaults will work for local development
```

#### 4. Database Setup
```bash
# Create migrations
python manage.py makemigrations

# Apply migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser
# Use these credentials for testing:
# Username: admin
# Email: <EMAIL>
# Password: quantnex123
```

#### 5. Start Backend Server
```bash
python manage.py runserver 8000
```

**Backend URLs:**
- API Base: http://localhost:8000/api/
- Admin Panel: http://localhost:8000/admin/
- API Docs: http://localhost:8000/api/docs/

### Frontend Setup (Next.js)

#### 1. Install Dependencies
```bash
cd quant-nex  # Your frontend directory
npm install
```

#### 2. Environment Configuration
Create `.env.local` file in the frontend root:
```env
# API Configuration
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000/api
NEXT_PUBLIC_API_TIMEOUT=10000

# Firebase Configuration (if using Firebase)
NEXT_PUBLIC_FIREBASE_API_KEY=your-firebase-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
```

#### 3. Update API Configuration
Create or update `lib/api.ts`:
```typescript
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api';

export const apiClient = {
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
};

// Helper function for authenticated requests
export const getAuthHeaders = () => {
  const token = localStorage.getItem('access_token');
  return token ? { Authorization: `Bearer ${token}` } : {};
};
```

#### 4. Start Frontend Server
```bash
npm run dev
```

**Frontend URL:** http://localhost:3000

---

## 🔗 Frontend-Backend Integration

### Authentication Integration

Update your frontend authentication context to use Django API:

```typescript
// contexts/auth-context.tsx
const login = async (email: string, password: string) => {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/login/`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: email, password }),
    });
    
    if (response.ok) {
      const data = await response.json();
      localStorage.setItem('access_token', data.access);
      localStorage.setItem('refresh_token', data.refresh);
      setUser(data.user);
      return data.user;
    }
    throw new Error('Login failed');
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
};
```

### API Service Integration

Create API service functions:

```typescript
// lib/api-services.ts
import { apiClient, getAuthHeaders } from './api';

export const patientService = {
  async getPatients() {
    const response = await fetch(`${apiClient.baseURL}/patients/`, {
      headers: { ...apiClient.headers, ...getAuthHeaders() },
    });
    return response.json();
  },
  
  async createPatient(patientData: any) {
    const response = await fetch(`${apiClient.baseURL}/patients/`, {
      method: 'POST',
      headers: { ...apiClient.headers, ...getAuthHeaders() },
      body: JSON.stringify(patientData),
    });
    return response.json();
  },
};

export const dashboardService = {
  async getOverview() {
    const response = await fetch(`${apiClient.baseURL}/dashboard/overview/`, {
      headers: { ...apiClient.headers, ...getAuthHeaders() },
    });
    return response.json();
  },
};
```

---

## 🧪 Testing the Integration

### 1. Test Authentication
1. Go to http://localhost:3000/login
2. Use credentials: `admin` / `quantnex123`
3. Should redirect to dashboard

### 2. Test API Endpoints
```bash
# Test login endpoint
curl -X POST http://localhost:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "quantnex123"}'

# Test protected endpoint (use token from login response)
curl -X GET http://localhost:8000/api/dashboard/overview/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 3. Test Frontend-Backend Communication
1. Open browser developer tools
2. Navigate through the application
3. Check Network tab for API calls
4. Verify responses are coming from Django backend

---

## 🐛 Troubleshooting

### Common Issues:

#### CORS Errors
If you see CORS errors in the browser console:
1. Check Django settings.py CORS configuration
2. Ensure frontend URL is in `CORS_ALLOWED_ORIGINS`
3. Restart Django server after changes

#### Authentication Issues
1. Check JWT token expiration
2. Verify API endpoints are correctly configured
3. Check browser localStorage for tokens

#### Database Issues
```bash
# Reset database (development only)
rm db.sqlite3
python manage.py migrate
python manage.py createsuperuser
```

#### Port Conflicts
- Backend: Change port with `python manage.py runserver 8001`
- Frontend: Change port with `npm run dev -- -p 3001`

---

## 📊 Development Workflow

### Recommended Development Setup:

1. **Terminal 1**: Django Backend
   ```bash
   cd backend
   source venv/bin/activate  # or venv\Scripts\activate
   python manage.py runserver 8000
   ```

2. **Terminal 2**: Next.js Frontend
   ```bash
   cd quant-nex
   npm run dev
   ```

3. **Terminal 3**: Additional commands (migrations, testing, etc.)

### Hot Reload:
- Django: Auto-reloads on file changes
- Next.js: Auto-reloads on file changes
- Both support hot module replacement

---

## 🚀 Production Deployment

### Backend (Django):
1. Set `DEBUG=False` in production
2. Configure production database (PostgreSQL)
3. Set up proper CORS origins
4. Use gunicorn for WSGI server
5. Configure static file serving

### Frontend (Next.js):
1. Build for production: `npm run build`
2. Deploy to Vercel, Netlify, or similar
3. Update API base URL for production

---

## 📚 Additional Resources

- **Django REST API Docs**: http://localhost:8000/api/docs/
- **Django Admin**: http://localhost:8000/admin/
- **Next.js Docs**: https://nextjs.org/docs
- **Django REST Framework**: https://www.django-rest-framework.org/

---

## 🆘 Getting Help

If you encounter issues:
1. Check the console logs (both frontend and backend)
2. Verify all dependencies are installed
3. Ensure both servers are running
4. Check the API documentation for correct endpoints
5. Review the troubleshooting section above

**Test Credentials:**
- Username: `admin`
- Password: `quantnex123`

Happy coding! 🎉
