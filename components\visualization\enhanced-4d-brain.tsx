"use client"

import { useRef, useState, use<PERSON>allback, useEffect } from "react"
import { <PERSON><PERSON>, use<PERSON>rame, useThree } from "@react-three/fiber"
import { OrbitControls, Environment, Html, Text, PerspectiveCamera, ContactShadows, Sphere } from "@react-three/drei"
import { <PERSON>lider } from "@/components/ui/slider"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  Eye, 
  EyeOff, 
  Maximize2, 
  Minimize2, 
  RotateCcw, 
  ZoomIn, 
  ZoomOut, 
  Move3D, 
  RotateCw,
  Target,
  Layers,
  Activity,
  Info,
  Play,
  Pause,
  Clock,
  Slice,
  Lightbulb
} from "lucide-react"
import * as THREE from "three"

interface Enhanced4DBrainProps {
  className?: string
}

export function Enhanced4DBrain({ className }: Enhanced4DBrainProps) {
  const [opacity, setOpacity] = useState(0.7)
  const [showLabe<PERSON>, setShowLabels] = useState(true)
  const [zoom, setZoom] = useState(1)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [autoRotate, setAutoRotate] = useState(false)
  const [rotationSpeed, setRotationSpeed] = useState(1)
  const [showWireframe, setShowWireframe] = useState(false)
  const [lightingIntensity, setLightingIntensity] = useState(1)
  const [selectedRegion, setSelectedRegion] = useState<string | null>(null)
  const [cameraPosition, setCameraPosition] = useState([0, 0, 5])
  const [showMeasurements, setShowMeasurements] = useState(false)
  
  // 4D Features
  const [timeAnimation, setTimeAnimation] = useState(false)
  const [timeSpeed, setTimeSpeed] = useState(1)
  const [layerDepth, setLayerDepth] = useState(0.5)
  const [crossSectionMode, setCrossSectionMode] = useState(false)
  const [crossSectionPosition, setCrossSectionPosition] = useState(0)
  const [dynamicLighting, setDynamicLighting] = useState(true)
  const [multiLayerView, setMultiLayerView] = useState(false)
  const [bloodFlowAnimation, setBloodFlowAnimation] = useState(false)
  const [neuralActivity, setNeuralActivity] = useState(false)
  
  const controlsRef = useRef<any>(null)

  const resetCamera = useCallback(() => {
    if (controlsRef.current) {
      controlsRef.current.reset()
    }
    setCameraPosition([0, 0, 5])
    setZoom(1)
  }, [])

  const zoomIn = useCallback(() => {
    setZoom(prev => Math.min(prev + 0.2, 3))
  }, [])

  const zoomOut = useCallback(() => {
    setZoom(prev => Math.max(prev - 0.2, 0.5))
  }, [])

  return (
    <div className={`${isFullscreen ? 'fixed inset-0 z-50 bg-black' : 'w-full h-[600px]'} rounded-xl overflow-hidden border border-blue-500/20 bg-gradient-to-br from-gray-900/50 to-blue-900/20 backdrop-blur-sm ${className}`}>
      <div className="relative w-full h-full">
        <Canvas 
          camera={{ position: cameraPosition as [number, number, number], fov: 45 }}
          shadows
          gl={{ antialias: true, alpha: true }}
        >
          <PerspectiveCamera makeDefault position={cameraPosition as [number, number, number]} fov={45} />
          
          {/* Enhanced Dynamic Lighting System */}
          <ambientLight intensity={0.2 * lightingIntensity} />
          <directionalLight 
            position={[10, 10, 10]} 
            intensity={0.8 * lightingIntensity} 
            castShadow
            shadow-mapSize-width={2048}
            shadow-mapSize-height={2048}
          />
          <pointLight position={[-10, -10, -10]} intensity={0.4 * lightingIntensity} color="#4f46e5" />
          <spotLight 
            position={[0, 10, 0]} 
            intensity={0.6 * lightingIntensity} 
            angle={0.3} 
            penumbra={0.5}
            color="#06b6d4"
            castShadow
          />
          
          {/* Dynamic Lighting for 4D Effect */}
          {dynamicLighting && (
            <>
              <pointLight position={[5, 5, 5]} intensity={0.3} color="#ff6b6b" />
              <pointLight position={[-5, -5, 5]} intensity={0.3} color="#4ecdc4" />
              <pointLight position={[0, 0, -5]} intensity={0.3} color="#45b7d1" />
            </>
          )}
          
          <Environment preset="studio" />
          <ContactShadows opacity={0.4} scale={10} blur={1} far={10} resolution={256} color="#000000" />

          <Enhanced4DBrainModel 
            opacity={opacity} 
            showLabels={showLabels} 
            zoom={zoom}
            showWireframe={showWireframe}
            onRegionSelect={setSelectedRegion}
            showMeasurements={showMeasurements}
            timeAnimation={timeAnimation}
            timeSpeed={timeSpeed}
            layerDepth={layerDepth}
            crossSectionMode={crossSectionMode}
            crossSectionPosition={crossSectionPosition}
            multiLayerView={multiLayerView}
            bloodFlowAnimation={bloodFlowAnimation}
            neuralActivity={neuralActivity}
            lightingIntensity={lightingIntensity}
          />

          <OrbitControls 
            ref={controlsRef}
            enablePan={true} 
            enableZoom={true} 
            enableRotate={true}
            autoRotate={autoRotate}
            autoRotateSpeed={rotationSpeed}
            enableDamping={true}
            dampingFactor={0.05}
            minDistance={2}
            maxDistance={20}
            maxPolarAngle={Math.PI}
            minPolarAngle={0}
          />
        </Canvas>

        {/* Enhanced 4D Control Panel */}
        <div className="absolute top-4 right-4 bg-gray-900/95 backdrop-blur-sm rounded-lg p-4 z-10 min-w-[320px] border border-blue-500/20 max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-semibold text-white flex items-center gap-2">
              <Activity className="h-4 w-4 text-medical-blue" />
              4D Brain Controls
            </h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsFullscreen(!isFullscreen)}
              className="text-gray-400 hover:text-white"
            >
              {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
            </Button>
          </div>

          <div className="space-y-4">
            {/* Time Animation Controls */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs text-gray-300 flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  Time Animation
                </span>
                <Badge variant={timeAnimation ? "default" : "secondary"} className="text-xs">
                  {timeAnimation ? "ON" : "OFF"}
                </Badge>
              </div>
              <div className="flex gap-2 mb-2">
                <Button
                  variant={timeAnimation ? "default" : "outline"}
                  size="sm"
                  onClick={() => setTimeAnimation(!timeAnimation)}
                  className="flex-1 text-xs"
                >
                  {timeAnimation ? <Pause className="h-3 w-3 mr-1" /> : <Play className="h-3 w-3 mr-1" />}
                  {timeAnimation ? "Pause" : "Play"}
                </Button>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-xs text-gray-400">Speed</span>
                <Slider
                  value={[timeSpeed]}
                  min={0.1}
                  max={3}
                  step={0.1}
                  onValueChange={(value) => setTimeSpeed(value[0])}
                  className="flex-1"
                  disabled={!timeAnimation}
                />
                <span className="text-xs text-gray-400">{timeSpeed.toFixed(1)}x</span>
              </div>
            </div>

            {/* Layer Controls */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs text-gray-300 flex items-center gap-1">
                  <Layers className="h-3 w-3" />
                  Layer Depth
                </span>
                <Badge variant="secondary" className="text-xs">{Math.round(layerDepth * 100)}%</Badge>
              </div>
              <Slider
                value={[layerDepth]}
                min={0}
                max={1}
                step={0.05}
                onValueChange={(value) => setLayerDepth(value[0])}
                className="w-full"
              />
            </div>

            {/* Cross Section Controls */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs text-gray-300 flex items-center gap-1">
                  <Slice className="h-3 w-3" />
                  Cross Section
                </span>
                <Badge variant={crossSectionMode ? "default" : "secondary"} className="text-xs">
                  {crossSectionMode ? "ON" : "OFF"}
                </Badge>
              </div>
              <div className="flex gap-2 mb-2">
                <Button
                  variant={crossSectionMode ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCrossSectionMode(!crossSectionMode)}
                  className="flex-1 text-xs"
                >
                  Toggle Section
                </Button>
              </div>
              {crossSectionMode && (
                <Slider
                  value={[crossSectionPosition]}
                  min={-1}
                  max={1}
                  step={0.05}
                  onValueChange={(value) => setCrossSectionPosition(value[0])}
                  className="w-full"
                />
              )}
            </div>

            {/* Advanced 4D Features */}
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant={multiLayerView ? "default" : "outline"}
                size="sm"
                onClick={() => setMultiLayerView(!multiLayerView)}
                className="text-xs"
              >
                <Layers className="h-3 w-3 mr-1" />
                Multi-Layer
              </Button>
              <Button
                variant={bloodFlowAnimation ? "default" : "outline"}
                size="sm"
                onClick={() => setBloodFlowAnimation(!bloodFlowAnimation)}
                className="text-xs"
              >
                <Activity className="h-3 w-3 mr-1" />
                Blood Flow
              </Button>
              <Button
                variant={neuralActivity ? "default" : "outline"}
                size="sm"
                onClick={() => setNeuralActivity(!neuralActivity)}
                className="text-xs"
              >
                <Target className="h-3 w-3 mr-1" />
                Neural Activity
              </Button>
              <Button
                variant={dynamicLighting ? "default" : "outline"}
                size="sm"
                onClick={() => setDynamicLighting(!dynamicLighting)}
                className="text-xs"
              >
                <Lightbulb className="h-3 w-3 mr-1" />
                Dynamic Light
              </Button>
            </div>
          </div>
        </div>

        {/* Selected Region Info */}
        {selectedRegion && (
          <div className="absolute bottom-4 left-4 bg-gray-900/90 backdrop-blur-sm rounded-lg p-3 z-10 border border-blue-500/20">
            <div className="flex items-center gap-2 mb-2">
              <Target className="h-4 w-4 text-medical-blue" />
              <span className="text-sm font-medium text-white">Selected Region</span>
            </div>
            <p className="text-xs text-gray-300">{selectedRegion}</p>
          </div>
        )}

        {/* 4D Interaction Hint */}
        <div className="absolute bottom-4 right-4 bg-black/50 text-white text-xs py-2 px-4 rounded-full backdrop-blur-sm">
          4D Mode: Time • Layers • Cross-sections • Neural Activity
        </div>
      </div>
    </div>
  )
}

function Enhanced4DBrainModel({
  opacity,
  showLabels,
  zoom,
  showWireframe,
  onRegionSelect,
  showMeasurements,
  timeAnimation,
  timeSpeed,
  layerDepth,
  crossSectionMode,
  crossSectionPosition,
  multiLayerView,
  bloodFlowAnimation,
  neuralActivity,
  lightingIntensity
}: {
  opacity: number;
  showLabels: boolean;
  zoom: number;
  showWireframe: boolean;
  onRegionSelect: (region: string | null) => void;
  showMeasurements: boolean;
  timeAnimation: boolean;
  timeSpeed: number;
  layerDepth: number;
  crossSectionMode: boolean;
  crossSectionPosition: number;
  multiLayerView: boolean;
  bloodFlowAnimation: boolean;
  neuralActivity: boolean;
  lightingIntensity: number;
}) {
  const brainRef = useRef<THREE.Group>(null)
  const cortexRef = useRef<THREE.Mesh>(null)
  const tumorRef = useRef<THREE.Mesh>(null)
  const vascularRef = useRef<THREE.Group>(null)
  const neuralRef = useRef<THREE.Group>(null)
  const [time, setTime] = useState(0)
  const [pulsePhase, setPulsePhase] = useState(0)

  useFrame((state, delta) => {
    const adjustedDelta = timeAnimation ? delta * timeSpeed : delta
    setTime(time + adjustedDelta)

    if (timeAnimation) {
      setPulsePhase(pulsePhase + adjustedDelta * 2)
    }

    if (brainRef.current && timeAnimation) {
      // 4D time-based rotation on multiple axes
      brainRef.current.rotation.x = Math.sin(time * 0.3) * 0.1
      brainRef.current.rotation.y += adjustedDelta * 0.2
      brainRef.current.rotation.z = Math.cos(time * 0.2) * 0.05
    }

    if (cortexRef.current) {
      // Breathing animation with time control
      const breathe = 1 + Math.sin(time * 2) * 0.01
      cortexRef.current.scale.setScalar(breathe)

      // Dynamic material properties
      if (cortexRef.current.material instanceof THREE.MeshPhysicalMaterial) {
        cortexRef.current.material.emissiveIntensity = 0.05 + Math.sin(time * 3) * 0.02
      }
    }

    if (tumorRef.current && bloodFlowAnimation) {
      // Pulsing tumor with blood flow simulation
      const pulse = 1 + Math.sin(time * 4) * 0.03
      tumorRef.current.scale.setScalar(pulse)
    }

    if (vascularRef.current && bloodFlowAnimation) {
      // Animated vascular network
      vascularRef.current.children.forEach((vessel, index) => {
        if (vessel instanceof THREE.Mesh && vessel.material instanceof THREE.MeshPhysicalMaterial) {
          const flowPhase = time * 2 + index * 0.5
          vessel.material.emissiveIntensity = 0.2 + Math.sin(flowPhase) * 0.15
        }
      })
    }

    if (neuralRef.current && neuralActivity) {
      // Neural activity simulation
      neuralRef.current.children.forEach((neuron, index) => {
        if (neuron instanceof THREE.Mesh) {
          const activityPhase = time * 3 + index * 0.3
          const activity = Math.sin(activityPhase) * 0.5 + 0.5
          neuron.scale.setScalar(0.8 + activity * 0.4)
        }
      })
    }
  })

  const handleBrainClick = () => {
    onRegionSelect("Enhanced 4D Brain Model - Multi-layered Visualization")
  }

  const handleTumorClick = () => {
    onRegionSelect("Glioblastoma Multiforme - 4D Temporal Analysis")
  }

  // Create cross-section clipping plane
  const clippingPlane = new THREE.Plane(new THREE.Vector3(1, 0, 0), crossSectionPosition)

  return (
    <group ref={brainRef} scale={[zoom, zoom, zoom]}>
      {/* Multi-layered Brain Structure */}
      {multiLayerView ? (
        // Multiple layers for 4D visualization
        <>
          {/* Outer Cortex Layer */}
          <mesh
            ref={cortexRef}
            position={[0, 0.2, 0]}
            scale={[1.3, 1.1, 0.95]}
            onClick={handleBrainClick}
            castShadow
            receiveShadow
          >
            <sphereGeometry args={[1.2, 128, 128]} />
            <meshPhysicalMaterial
              color={new THREE.Color().setHSL(0.6, 0.3, 0.7)}
              transparent
              opacity={opacity * layerDepth}
              roughness={0.3}
              metalness={0.05}
              clearcoat={0.8}
              clearcoatRoughness={0.1}
              emissive={new THREE.Color().setHSL(0.6, 0.5, 0.1)}
              emissiveIntensity={0.05 * lightingIntensity}
              wireframe={showWireframe}
              clippingPlanes={crossSectionMode ? [clippingPlane] : []}
            />
          </mesh>

          {/* Middle Layer - White Matter */}
          <mesh
            position={[0, 0.2, 0]}
            scale={[1.1, 0.9, 0.8]}
            castShadow
          >
            <sphereGeometry args={[1.0, 96, 96]} />
            <meshPhysicalMaterial
              color={new THREE.Color().setHSL(0.1, 0.2, 0.9)}
              transparent
              opacity={opacity * layerDepth * 0.7}
              roughness={0.4}
              metalness={0.02}
              transmission={0.3}
              thickness={0.5}
              ior={1.4}
              clippingPlanes={crossSectionMode ? [clippingPlane] : []}
            />
          </mesh>

          {/* Inner Layer - Gray Matter */}
          <mesh
            position={[0, 0.2, 0]}
            scale={[0.9, 0.7, 0.6]}
          >
            <sphereGeometry args={[0.8, 64, 64]} />
            <meshPhysicalMaterial
              color={new THREE.Color().setHSL(0.0, 0.1, 0.6)}
              transparent
              opacity={opacity * layerDepth * 0.5}
              roughness={0.6}
              metalness={0.01}
              clippingPlanes={crossSectionMode ? [clippingPlane] : []}
            />
          </mesh>
        </>
      ) : (
        // Single brain structure
        <mesh
          ref={cortexRef}
          position={[0, 0.2, 0]}
          scale={[1.3, 1.1, 0.95]}
          onClick={handleBrainClick}
          castShadow
          receiveShadow
        >
          <sphereGeometry args={[1.2, 128, 128]} />
          <meshPhysicalMaterial
            color="#f8b4b4"
            transparent
            opacity={opacity}
            roughness={0.4}
            metalness={0.05}
            clearcoat={0.6}
            clearcoatRoughness={0.2}
            emissive="#fca5a5"
            emissiveIntensity={0.05 * lightingIntensity}
            wireframe={showWireframe}
            clippingPlanes={crossSectionMode ? [clippingPlane] : []}
          />
        </mesh>
      )}

      {/* Enhanced Tumor with 4D Features */}
      <mesh
        ref={tumorRef}
        position={[0.8, 0.1, 0.2]}
        onClick={handleTumorClick}
        castShadow
      >
        <sphereGeometry args={[0.25, 64, 64]} />
        <meshPhysicalMaterial
          color="#dc2626"
          transparent
          opacity={opacity * 1.2}
          roughness={0.3}
          metalness={0.1}
          emissive="#7f1d1d"
          emissiveIntensity={0.2 * lightingIntensity}
          clippingPlanes={crossSectionMode ? [clippingPlane] : []}
        />
        {showLabels && (
          <Html position={[0.4, 0.3, 0]} center>
            <div className="bg-red-900/90 text-white text-xs px-3 py-2 rounded-lg border border-red-500/50 backdrop-blur-sm">
              <div className="font-semibold">4D Glioblastoma</div>
              <div className="text-red-200">Temporal analysis</div>
              <div className="text-red-200">Growth rate: {timeSpeed.toFixed(1)}x</div>
            </div>
          </Html>
        )}
      </mesh>

      {/* Enhanced Vascular Network with Blood Flow */}
      <group ref={vascularRef}>
        {Array.from({ length: 16 }, (_, i) => (
          <mesh key={i} position={[
            Math.cos(i * Math.PI / 8) * 1.2,
            Math.sin(i * Math.PI / 8) * 0.3,
            Math.sin(i * Math.PI / 6) * 1.2
          ]}>
            <cylinderGeometry args={[0.015, 0.015, 2.5, 12]} />
            <meshPhysicalMaterial
              color="#ef4444"
              transparent
              opacity={opacity * 0.8}
              emissive="#dc2626"
              emissiveIntensity={bloodFlowAnimation ? 0.4 * lightingIntensity : 0.2 * lightingIntensity}
              clippingPlanes={crossSectionMode ? [clippingPlane] : []}
            />
          </mesh>
        ))}
      </group>

      {/* Neural Activity Visualization */}
      {neuralActivity && (
        <group ref={neuralRef}>
          {Array.from({ length: 20 }, (_, i) => (
            <mesh key={i} position={[
              (Math.random() - 0.5) * 2,
              (Math.random() - 0.5) * 2,
              (Math.random() - 0.5) * 2
            ]}>
              <sphereGeometry args={[0.02, 8, 8]} />
              <meshBasicMaterial
                color="#00ff88"
                transparent
                opacity={0.8}
              />
            </mesh>
          ))}
        </group>
      )}

      {/* 4D Measurement Markers */}
      {showMeasurements && (
        <group>
          <mesh position={[-1.3, 0.2, 0]}>
            <sphereGeometry args={[0.03, 8, 8]} />
            <meshBasicMaterial color="#10b981" />
          </mesh>
          <mesh position={[1.3, 0.2, 0]}>
            <sphereGeometry args={[0.03, 8, 8]} />
            <meshBasicMaterial color="#10b981" />
          </mesh>
          <mesh position={[0, 0.2, 0]} rotation={[0, 0, Math.PI / 2]}>
            <cylinderGeometry args={[0.005, 0.005, 2.6, 8]} />
            <meshBasicMaterial color="#10b981" />
          </mesh>
          <Html position={[0, 0.5, 0]} center>
            <div className="bg-green-900/90 text-green-100 text-xs px-2 py-1 rounded border border-green-500/50">
              4D Analysis: {(15.6 * timeSpeed).toFixed(1)} cm/s
            </div>
          </Html>
        </group>
      )}

      {/* Medical Coordinate System with 4D Enhancement */}
      {showLabels && (
        <>
          <Text position={[2.2, 0, 0]} color="#ef4444" fontSize={0.12} fontWeight="bold">
            Right
          </Text>
          <Text position={[-2.2, 0, 0]} color="#ef4444" fontSize={0.12} fontWeight="bold">
            Left
          </Text>
          <Text position={[0, 2.2, 0]} color="#22c55e" fontSize={0.12} fontWeight="bold">
            Superior
          </Text>
          <Text position={[0, -2.2, 0]} color="#22c55e" fontSize={0.12} fontWeight="bold">
            Inferior
          </Text>
          <Text position={[0, 0, 2.2]} color="#3b82f6" fontSize={0.12} fontWeight="bold">
            Anterior
          </Text>
          <Text position={[0, 0, -2.2]} color="#3b82f6" fontSize={0.12} fontWeight="bold">
            Posterior
          </Text>
          <Text position={[0, 0, 0]} color="#fbbf24" fontSize={0.08} fontWeight="bold">
            4D Core
          </Text>
        </>
      )}
    </group>
  )
}
