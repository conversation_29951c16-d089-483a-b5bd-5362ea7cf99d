#!/usr/bin/env python3
"""
Complete User Journey Test for Quant-NEX
Tests the full flow from landing page to dashboard
"""

import requests
import time

def test_landing_page():
    """Test landing page accessibility and navigation"""
    print("🏠 Testing Landing Page...")
    
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200:
            print("✅ Landing page accessible")
            # Check if it contains expected content
            content = response.text.lower()
            if "quant-nex" in content and "get started" in content:
                print("✅ Landing page content verified")
                return True
            else:
                print("⚠️ Landing page content incomplete")
                return False
        else:
            print(f"❌ Landing page failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Landing page error: {e}")
        return False

def test_login_page():
    """Test login page accessibility"""
    print("\n🔐 Testing Login Page...")
    
    try:
        response = requests.get("http://localhost:3000/login", timeout=5)
        if response.status_code == 200:
            print("✅ Login page accessible")
            return True
        else:
            print(f"❌ Login page failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Login page error: {e}")
        return False

def test_authentication_api():
    """Test authentication API endpoints"""
    print("\n🔑 Testing Authentication API...")
    
    # Test login
    login_data = {
        "username": "admin",
        "password": "quantnex123"
    }
    
    try:
        response = requests.post("http://localhost:8000/api/auth/login/", json=login_data)
        if response.status_code == 200:
            data = response.json()
            print("✅ Login API working")
            print(f"   User: {data['user']['display_name']}")
            print(f"   Role: {data['user']['role']}")
            
            # Test protected endpoint
            headers = {"Authorization": f"Bearer {data['access']}"}
            profile_response = requests.get("http://localhost:8000/api/auth/profile/", headers=headers)
            
            if profile_response.status_code == 200:
                print("✅ Protected endpoints accessible")
                return data['access']
            else:
                print(f"❌ Protected endpoint failed: {profile_response.status_code}")
                return None
        else:
            print(f"❌ Login API failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Authentication API error: {e}")
        return None

def test_dashboard_api(token):
    """Test dashboard API endpoints"""
    print("\n📊 Testing Dashboard API...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    endpoints = [
        ("Dashboard Overview", "/dashboard/overview/"),
        ("Patient Stats", "/patients/stats/"),
        ("User Profile", "/auth/profile/"),
    ]
    
    working_endpoints = 0
    for name, endpoint in endpoints:
        try:
            response = requests.get(f"http://localhost:8000/api{endpoint}", headers=headers)
            if response.status_code == 200:
                print(f"✅ {name} API working")
                working_endpoints += 1
            else:
                print(f"⚠️ {name} API: {response.status_code}")
        except Exception as e:
            print(f"❌ {name} API error: {e}")
    
    return working_endpoints > 0

def test_frontend_pages():
    """Test all frontend pages"""
    print("\n🌐 Testing Frontend Pages...")
    
    pages = [
        ("Dashboard", "/dashboard"),
        ("Patients", "/patients"),
        ("Diagnosis", "/diagnosis"),
        ("Treatment", "/treatment"),
        ("Reports", "/reports"),
        ("Monitoring", "/monitoring"),
    ]
    
    accessible_pages = 0
    for name, path in pages:
        try:
            response = requests.get(f"http://localhost:3000{path}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {name} page accessible")
                accessible_pages += 1
            else:
                print(f"⚠️ {name} page: {response.status_code}")
        except Exception as e:
            print(f"❌ {name} page error: {e}")
    
    return accessible_pages

def test_cors_configuration():
    """Test CORS configuration"""
    print("\n🌍 Testing CORS Configuration...")
    
    headers = {
        'Origin': 'http://localhost:3000',
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type,Authorization'
    }
    
    try:
        response = requests.options("http://localhost:8000/api/auth/login/", headers=headers)
        if response.status_code in [200, 204]:
            cors_headers = response.headers
            if 'Access-Control-Allow-Origin' in cors_headers:
                print("✅ CORS properly configured")
                return True
            else:
                print("❌ CORS headers missing")
                return False
        else:
            print(f"❌ CORS preflight failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ CORS test error: {e}")
        return False

def main():
    """Run complete user journey test"""
    print("🏥 Quant-NEX Complete User Journey Test")
    print("=" * 60)
    
    # Test 1: Landing Page
    landing_ok = test_landing_page()
    
    # Test 2: Login Page
    login_page_ok = test_login_page()
    
    # Test 3: Authentication API
    auth_token = test_authentication_api()
    auth_ok = auth_token is not None
    
    # Test 4: Dashboard API
    dashboard_api_ok = test_dashboard_api(auth_token) if auth_token else False
    
    # Test 5: Frontend Pages
    accessible_pages = test_frontend_pages()
    frontend_ok = accessible_pages >= 4  # At least 4 pages should be accessible
    
    # Test 6: CORS
    cors_ok = test_cors_configuration()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 COMPLETE USER JOURNEY TEST RESULTS")
    print("=" * 60)
    
    tests = [
        ("Landing Page", landing_ok),
        ("Login Page", login_page_ok),
        ("Authentication API", auth_ok),
        ("Dashboard API", dashboard_api_ok),
        ("Frontend Pages", frontend_ok),
        ("CORS Configuration", cors_ok),
    ]
    
    passed_tests = 0
    for test_name, result in tests:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<20}: {status}")
        if result:
            passed_tests += 1
    
    print(f"\nOverall Score: {passed_tests}/{len(tests)} tests passed")
    
    if passed_tests >= 5:
        print("\n🎉 EXCELLENT! Full-stack integration is working correctly!")
        print("\n🚀 Ready for Production Testing:")
        print("   1. Landing Page: http://localhost:3000")
        print("   2. Login: http://localhost:3000/login")
        print("   3. Dashboard: http://localhost:3000/dashboard")
        print("   4. API Docs: http://localhost:8000/api/docs/")
        print("\n🔑 Test Credentials:")
        print("   Username: admin")
        print("   Password: quantnex123")
        
        print("\n✨ User Journey Flow:")
        print("   1. Visit landing page → Click 'Get Started'")
        print("   2. Login with credentials")
        print("   3. Access dashboard and all features")
        print("   4. Navigate between different modules")
        
        return 0
    else:
        print(f"\n⚠️ Some issues detected. {6-passed_tests} tests failed.")
        print("Please review the errors above and fix them.")
        return 1

if __name__ == '__main__':
    exit(main())
