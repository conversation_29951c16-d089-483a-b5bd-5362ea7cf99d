"""
Diagnosis views for Quant-NEX Healthcare Application.
"""
from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from .models import Diagnosis, DiagnosticTest, ImagingStudy
from .serializers import DiagnosisSerializer, DiagnosticTestSerializer, ImagingStudySerializer


class DiagnosisListCreateView(generics.ListCreateAPIView):
    """
    List and create diagnoses.
    """
    queryset = Diagnosis.objects.all()
    serializer_class = DiagnosisSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        user = self.request.user
        queryset = Diagnosis.objects.all()
        
        # Filter by doctor if user is a doctor
        if user.role == 'doctor':
            queryset = queryset.filter(doctor=user)
        
        return queryset.order_by('-created_at')


class DiagnosisDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update, or delete a diagnosis.
    """
    queryset = Diagnosis.objects.all()
    serializer_class = DiagnosisSerializer
    permission_classes = [permissions.IsAuthenticated]


class PatientDiagnosisListView(generics.ListAPIView):
    """
    List diagnoses for a specific patient.
    """
    serializer_class = DiagnosisSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        patient_id = self.kwargs['patient_id']
        return Diagnosis.objects.filter(patient_id=patient_id).order_by('-created_at')


class DiagnosticTestListCreateView(generics.ListCreateAPIView):
    """
    List and create diagnostic tests.
    """
    queryset = DiagnosticTest.objects.all()
    serializer_class = DiagnosticTestSerializer
    permission_classes = [permissions.IsAuthenticated]


class DiagnosticTestDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update, or delete a diagnostic test.
    """
    queryset = DiagnosticTest.objects.all()
    serializer_class = DiagnosticTestSerializer
    permission_classes = [permissions.IsAuthenticated]


class ImagingStudyListCreateView(generics.ListCreateAPIView):
    """
    List and create imaging studies.
    """
    queryset = ImagingStudy.objects.all()
    serializer_class = ImagingStudySerializer
    permission_classes = [permissions.IsAuthenticated]


class ImagingStudyDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update, or delete an imaging study.
    """
    queryset = ImagingStudy.objects.all()
    serializer_class = ImagingStudySerializer
    permission_classes = [permissions.IsAuthenticated]


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_3d_model_data(request, study_id):
    """
    Get 3D model data for an imaging study.
    """
    try:
        study = ImagingStudy.objects.get(id=study_id)
        return Response({
            'model_file_path': study.model_file_path,
            'model_format': study.model_format,
            'default_opacity': study.default_opacity,
            'default_color': study.default_color,
            'enable_4d_features': study.enable_4d_features,
            'measurements': study.measurements,
        })
    except ImagingStudy.DoesNotExist:
        return Response(
            {'error': 'Imaging study not found'}, 
            status=status.HTTP_404_NOT_FOUND
        )
