#!/usr/bin/env python3
"""
Test both servers and complete application functionality
"""

import requests
import time
import json

def wait_for_server(url, name, max_attempts=30):
    """Wait for server to be ready"""
    print(f"⏳ Waiting for {name}...")
    
    for i in range(max_attempts):
        try:
            response = requests.get(url, timeout=3)
            if response.status_code == 200:
                print(f"✅ {name}: READY")
                return True
        except:
            pass
        time.sleep(1)
    
    print(f"❌ {name}: TIMEOUT")
    return False

def test_authentication():
    """Test authentication functionality"""
    print("\n🔐 Testing Authentication...")
    
    try:
        response = requests.post("http://localhost:8000/api/auth/login/", 
                               json={"username": "admin", "password": "quantnex123"},
                               timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Authentication: SUCCESS")
            print(f"   User: {data['user']['display_name']}")
            return data['access']
        else:
            print(f"❌ Authentication: FAILED ({response.status_code})")
            return None
    except Exception as e:
        print(f"❌ Authentication: ERROR - {e}")
        return None

def test_api_proxy():
    """Test API proxy through Next.js"""
    print("\n🌐 Testing API Proxy...")
    
    try:
        response = requests.post("http://localhost:3000/api/auth/login/", 
                               json={"username": "admin", "password": "quantnex123"},
                               timeout=10)
        
        if response.status_code == 200:
            print("✅ API Proxy: WORKING")
            return True
        else:
            print(f"❌ API Proxy: FAILED ({response.status_code})")
            return False
    except Exception as e:
        print(f"❌ API Proxy: ERROR - {e}")
        return False

def test_frontend_pages():
    """Test frontend pages"""
    print("\n📱 Testing Frontend Pages...")
    
    pages = [
        ("Landing Page", "http://localhost:3000"),
        ("Login Page", "http://localhost:3000/login"),
        ("Dashboard", "http://localhost:3000/dashboard"),
        ("Treatment", "http://localhost:3000/treatment"),
        ("Diagnosis", "http://localhost:3000/diagnosis"),
    ]
    
    working_pages = 0
    for name, url in pages:
        try:
            response = requests.get(url, timeout=8)
            if response.status_code == 200:
                print(f"✅ {name}: ACCESSIBLE")
                working_pages += 1
            else:
                print(f"❌ {name}: FAILED ({response.status_code})")
        except Exception as e:
            print(f"❌ {name}: ERROR - {e}")
    
    return working_pages >= 4

def main():
    """Run complete application test"""
    print("🚀 QUANT-NEX APPLICATION STARTUP TEST")
    print("=" * 50)
    
    # Wait for Django backend
    django_ready = wait_for_server("http://localhost:8000/api/docs/", "Django Backend")
    
    # Wait for Next.js frontend
    nextjs_ready = wait_for_server("http://localhost:3000", "Next.js Frontend")
    
    if not django_ready or not nextjs_ready:
        print("\n❌ Server startup failed!")
        return False
    
    # Test authentication
    auth_token = test_authentication()
    
    # Test API proxy
    proxy_working = test_api_proxy()
    
    # Test frontend pages
    frontend_working = test_frontend_pages()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 APPLICATION STATUS REPORT")
    print("=" * 50)
    
    results = [
        ("Django Backend", django_ready),
        ("Next.js Frontend", nextjs_ready),
        ("Authentication", auth_token is not None),
        ("API Proxy", proxy_working),
        ("Frontend Pages", frontend_working),
    ]
    
    all_working = True
    for component, status in results:
        status_text = "✅ WORKING" if status else "❌ FAILED"
        print(f"{component:<20}: {status_text}")
        if not status:
            all_working = False
    
    if all_working:
        print("\n🎉 APPLICATION FULLY OPERATIONAL!")
        print("\n📍 ACCESS POINTS:")
        print("   🌐 Main Application: http://localhost:3000")
        print("   🔐 Login Page:       http://localhost:3000/login")
        print("   📊 Dashboard:        http://localhost:3000/dashboard")
        print("   🔧 API Docs:         http://localhost:3000/api/docs/")
        print("\n🔑 TEST CREDENTIALS:")
        print("   Username: admin")
        print("   Password: quantnex123")
        print("\n✨ FEATURES READY:")
        print("   • Single-page hosting (all on localhost:3000)")
        print("   • 3D medical visualizations")
        print("   • Real-time dashboard")
        print("   • Secure authentication")
        print("   • Complete healthcare management")
    else:
        print("\n⚠️ Some components failed. Check individual results above.")
    
    return all_working

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
