#!/usr/bin/env python3
"""
Test the login fix with improved error handling
"""

import requests
import json

def test_direct_django_login():
    """Test direct Django login"""
    print("🔐 Testing Direct Django Login...")
    
    try:
        response = requests.post("http://localhost:8000/api/auth/login/", 
                               json={"username": "admin", "password": "quantnex123"},
                               timeout=10)
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("✅ Direct Django Login: SUCCESS")
            print(f"User: {data['user']['display_name']}")
            return data['access']
        else:
            print(f"❌ Direct Django Login: FAILED")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Direct Django Login: ERROR - {e}")
        return None

def test_nextjs_proxy_login():
    """Test login through Next.js proxy"""
    print("\n🌐 Testing Next.js Proxy Login...")
    
    try:
        response = requests.post("http://localhost:3000/api/auth/login/", 
                               json={"username": "admin", "password": "quantnex123"},
                               timeout=10)
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("✅ Next.js Proxy Login: SUCCESS")
            print(f"User: {data['user']['display_name']}")
            return True
        else:
            print(f"❌ Next.js Proxy Login: FAILED")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Next.js Proxy Login: ERROR - {e}")
        return False

def test_frontend_pages():
    """Test frontend pages"""
    print("\n📱 Testing Frontend Pages...")
    
    pages = [
        ("Landing Page", "http://localhost:3000"),
        ("Login Page", "http://localhost:3000/login"),
        ("Dashboard", "http://localhost:3000/dashboard"),
    ]
    
    all_working = True
    for name, url in pages:
        try:
            response = requests.get(url, timeout=8)
            if response.status_code == 200:
                print(f"✅ {name}: ACCESSIBLE")
            else:
                print(f"❌ {name}: FAILED ({response.status_code})")
                all_working = False
        except Exception as e:
            print(f"❌ {name}: ERROR - {e}")
            all_working = False
    
    return all_working

def main():
    """Run all tests"""
    print("🚨 TESTING LOGIN FIX & SINGLE-PAGE HOSTING")
    print("=" * 60)
    
    # Test 1: Direct Django login
    django_token = test_direct_django_login()
    
    # Test 2: Next.js proxy login
    proxy_working = test_nextjs_proxy_login()
    
    # Test 3: Frontend pages
    frontend_working = test_frontend_pages()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    results = [
        ("Direct Django Login", django_token is not None),
        ("Next.js Proxy Login", proxy_working),
        ("Frontend Pages", frontend_working),
    ]
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name:<25}: {status}")
        if not passed:
            all_passed = False
    
    print(f"\nOverall Status: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n🎉 LOGIN FIX SUCCESSFUL!")
        print("🌐 Single-page hosting ready!")
        print("\n📋 NEXT STEPS:")
        print("1. Open browser: http://localhost:3000")
        print("2. Navigate to login: http://localhost:3000/login")
        print("3. Login with: admin / quantnex123")
        print("4. Check browser console for any errors")
        print("5. Test all features and 3D visualizations")
    else:
        print("\n⚠️ Some issues detected. Check individual test results.")
    
    return all_passed

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
