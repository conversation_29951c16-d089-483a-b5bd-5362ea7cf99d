#!/usr/bin/env python3
"""
Test dashboard API endpoints
"""

import requests

def test_dashboard_endpoints():
    """Test all dashboard API endpoints"""
    print("📊 Testing Dashboard API Endpoints...")
    
    # First login to get token
    login_data = {
        "username": "admin",
        "password": "quantnex123"
    }
    
    try:
        response = requests.post("http://localhost:8000/api/auth/login/", json=login_data)
        if response.status_code != 200:
            print(f"❌ Login failed: {response.status_code}")
            return
        
        data = response.json()
        token = data['access']
        headers = {"Authorization": f"Bearer {token}"}
        
        print("✅ Login successful")
        
        # Test dashboard endpoints
        endpoints = [
            ("Dashboard Overview", "/dashboard/overview/"),
            ("Patient Statistics", "/dashboard/patient-stats/"),
            ("Appointment Analytics", "/dashboard/appointment-analytics/"),
            ("System Status", "/dashboard/system-status/"),
            ("System Alerts", "/dashboard/alerts/"),
        ]
        
        for name, endpoint in endpoints:
            try:
                response = requests.get(f"http://localhost:8000/api{endpoint}", headers=headers)
                print(f"{name}: {response.status_code}")
                
                if response.status_code == 200:
                    print(f"✅ {name} working")
                elif response.status_code == 404:
                    print(f"⚠️ {name} endpoint not found")
                elif response.status_code == 500:
                    print(f"❌ {name} server error")
                    print(f"   Error: {response.text[:200]}...")
                else:
                    print(f"⚠️ {name} unexpected status: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ {name} error: {e}")
        
    except Exception as e:
        print(f"❌ Test error: {e}")

if __name__ == '__main__':
    test_dashboard_endpoints()
