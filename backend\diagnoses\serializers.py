"""
Diagnosis serializers for Quant-NEX Healthcare Application.
"""
from rest_framework import serializers
from .models import Diagnosis, DiagnosticTest, ImagingStudy


class DiagnosisSerializer(serializers.ModelSerializer):
    """
    Diagnosis serializer.
    """
    patient_name = serializers.Char<PERSON><PERSON>(
        source='patient.full_name', 
        read_only=True
    )
    doctor_name = serializers.CharField(
        source='doctor.display_name', 
        read_only=True
    )
    
    class Meta:
        model = Diagnosis
        fields = [
            'id', 'patient', 'patient_name', 'doctor', 'doctor_name',
            'diagnosis_code', 'diagnosis_name', 'diagnosis_type', 'severity',
            'symptoms', 'clinical_findings', 'differential_diagnoses',
            'cancer_stage', 'cancer_grade', 'metastasis_sites',
            'prognosis', 'survival_estimate', 'is_active',
            'confirmed_date', 'resolved_date', 'notes',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class DiagnosticTestSerializer(serializers.ModelSerializer):
    """
    Diagnostic test serializer.
    """
    patient_name = serializers.<PERSON>r<PERSON><PERSON>(
        source='patient.full_name', 
        read_only=True
    )
    ordered_by_name = serializers.CharField(
        source='ordered_by.display_name', 
        read_only=True
    )
    
    class Meta:
        model = DiagnosticTest
        fields = [
            'id', 'patient', 'patient_name', 'diagnosis', 'ordered_by',
            'ordered_by_name', 'test_type', 'test_name', 'test_code',
            'description', 'ordered_date', 'scheduled_date', 'completed_date',
            'status', 'results', 'normal_range', 'is_abnormal',
            'critical_values', 'result_files', 'images', 'notes',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'ordered_date', 'created_at', 'updated_at']


class ImagingStudySerializer(serializers.ModelSerializer):
    """
    Imaging study serializer.
    """
    patient_name = serializers.CharField(
        source='patient.full_name', 
        read_only=True
    )
    
    class Meta:
        model = ImagingStudy
        fields = [
            'id', 'patient', 'patient_name', 'diagnostic_test',
            'study_id', 'modality', 'study_description', 'body_part',
            'acquisition_date', 'slice_thickness', 'pixel_spacing',
            'matrix_size', 'model_file_path', 'model_format', 'model_size',
            'default_opacity', 'default_color', 'enable_4d_features',
            'dicom_files', 'series_uid', 'study_uid', 'findings',
            'measurements', 'ai_analysis', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
