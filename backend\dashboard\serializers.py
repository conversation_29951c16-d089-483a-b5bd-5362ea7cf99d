"""
Dashboard serializers for Quant-NEX Healthcare Application.
"""
from rest_framework import serializers
from .models import DashboardWidget, SystemAlert, UserActivity, SystemMetrics


class DashboardWidgetSerializer(serializers.ModelSerializer):
    """
    Dashboard widget serializer.
    """
    class Meta:
        model = DashboardWidget
        fields = [
            'id', 'widget_type', 'title', 'position_x', 'position_y',
            'width', 'height', 'is_visible', 'configuration',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class SystemAlertSerializer(serializers.ModelSerializer):
    """
    System alert serializer.
    """
    created_by_name = serializers.CharField(
        source='created_by.display_name', 
        read_only=True
    )
    
    class Meta:
        model = SystemAlert
        fields = [
            'id', 'title', 'message', 'alert_type', 'priority',
            'start_time', 'end_time', 'is_active', 'is_global',
            'created_by_name', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class UserActivitySerializer(serializers.ModelSerializer):
    """
    User activity serializer.
    """
    user_name = serializers.CharField(
        source='user.display_name', 
        read_only=True
    )
    
    class Meta:
        model = UserActivity
        fields = [
            'id', 'user_name', 'activity_type', 'description',
            'patient_id', 'object_id', 'object_type', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class SystemMetricsSerializer(serializers.ModelSerializer):
    """
    System metrics serializer.
    """
    class Meta:
        model = SystemMetrics
        fields = [
            'id', 'metric_type', 'value', 'unit', 'date',
            'hour', 'metadata', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class DashboardStatsSerializer(serializers.Serializer):
    """
    Dashboard statistics serializer.
    """
    patient_stats = serializers.DictField()
    appointment_stats = serializers.DictField()
    recent_activities = UserActivitySerializer(many=True)
    active_alerts = SystemAlertSerializer(many=True)
    user_info = serializers.DictField()


class PatientAnalyticsSerializer(serializers.Serializer):
    """
    Patient analytics serializer.
    """
    total_patients = serializers.IntegerField()
    status_distribution = serializers.ListField()
    cancer_type_distribution = serializers.ListField()
    age_distribution = serializers.DictField()
    gender_distribution = serializers.ListField()
    treatment_progress_distribution = serializers.DictField()


class AppointmentAnalyticsSerializer(serializers.Serializer):
    """
    Appointment analytics serializer.
    """
    today_total = serializers.IntegerField()
    today_completed = serializers.IntegerField()
    today_pending = serializers.IntegerField()
    week_total = serializers.IntegerField()
    type_distribution = serializers.ListField()
    status_distribution = serializers.ListField()
    upcoming_appointments = serializers.ListField()


class SystemStatusSerializer(serializers.Serializer):
    """
    System status serializer.
    """
    health_status = serializers.DictField()
    metrics = serializers.DictField()
    last_updated = serializers.DateTimeField()
