"""
Authentication models for Quant-NEX Healthcare Application.
"""
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.core.validators import RegexValidator


class User(AbstractUser):
    """
    Custom User model for healthcare professionals.
    Extends Django's AbstractUser with healthcare-specific fields.
    """
    
    ROLE_CHOICES = [
        ('doctor', 'Doctor'),
        ('nurse', 'Nurse'),
        ('admin', 'Administrator'),
        ('technician', 'Medical Technician'),
        ('researcher', 'Researcher'),
    ]
    
    SPECIALIZATION_CHOICES = [
        ('oncology', 'Oncology'),
        ('neurology', 'Neurology'),
        ('radiology', 'Radiology'),
        ('pathology', 'Pathology'),
        ('surgery', 'Surgery'),
        ('internal_medicine', 'Internal Medicine'),
        ('pediatrics', 'Pediatrics'),
        ('other', 'Other'),
    ]
    
    # Personal Information
    first_name = models.CharField(max_length=150, blank=False)
    last_name = models.CharField(max_length=150, blank=False)
    middle_name = models.CharField(max_length=150, blank=True)
    
    # Professional Information
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, default='doctor')
    specialization = models.CharField(max_length=30, choices=SPECIALIZATION_CHOICES, blank=True)
    license_number = models.CharField(max_length=50, unique=True, blank=True)
    department = models.CharField(max_length=100, blank=True)
    hospital_name = models.CharField(max_length=200, blank=True)
    
    # Contact Information
    phone_regex = RegexValidator(
        regex=r'^\+?1?\d{9,15}$',
        message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed."
    )
    phone_number = models.CharField(validators=[phone_regex], max_length=17, blank=True)
    emergency_contact = models.CharField(max_length=17, blank=True)
    
    # Profile Information
    profile_picture = models.ImageField(upload_to='profile_pictures/', blank=True, null=True)
    bio = models.TextField(max_length=500, blank=True)
    years_of_experience = models.PositiveIntegerField(default=0)
    
    # System Information
    is_verified = models.BooleanField(default=False)
    last_login_ip = models.GenericIPAddressField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'auth_user'
        verbose_name = 'User'
        verbose_name_plural = 'Users'
    
    def __str__(self):
        return f"Dr. {self.first_name} {self.last_name}" if self.role == 'doctor' else f"{self.first_name} {self.last_name}"
    
    @property
    def full_name(self):
        """Return the user's full name."""
        if self.middle_name:
            return f"{self.first_name} {self.middle_name} {self.last_name}"
        return f"{self.first_name} {self.last_name}"
    
    @property
    def display_name(self):
        """Return the user's display name with title."""
        title = "Dr." if self.role == 'doctor' else ""
        return f"{title} {self.full_name}".strip()


class UserSession(models.Model):
    """
    Track user sessions for security and analytics.
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sessions')
    session_key = models.CharField(max_length=40, unique=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    login_time = models.DateTimeField(auto_now_add=True)
    logout_time = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'user_sessions'
        ordering = ['-login_time']
    
    def __str__(self):
        return f"{self.user.display_name} - {self.login_time}"


class UserPreferences(models.Model):
    """
    Store user preferences and settings.
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='preferences')
    
    # UI Preferences
    theme = models.CharField(max_length=20, default='dark', choices=[
        ('light', 'Light'),
        ('dark', 'Dark'),
        ('auto', 'Auto'),
    ])
    language = models.CharField(max_length=10, default='en', choices=[
        ('en', 'English'),
        ('hi', 'Hindi'),
        ('mr', 'Marathi'),
        ('ta', 'Tamil'),
        ('te', 'Telugu'),
    ])
    
    # Dashboard Preferences
    default_dashboard_view = models.CharField(max_length=30, default='overview')
    show_patient_photos = models.BooleanField(default=True)
    auto_refresh_interval = models.PositiveIntegerField(default=300)  # seconds
    
    # Notification Preferences
    email_notifications = models.BooleanField(default=True)
    sms_notifications = models.BooleanField(default=False)
    critical_alerts = models.BooleanField(default=True)
    
    # 3D Model Preferences
    default_model_quality = models.CharField(max_length=20, default='high', choices=[
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('ultra', 'Ultra'),
    ])
    enable_4d_features = models.BooleanField(default=True)
    auto_rotate_models = models.BooleanField(default=False)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'user_preferences'
    
    def __str__(self):
        return f"{self.user.display_name} Preferences"
