"""
Diagnosis admin configuration for Quant-NEX Healthcare Application.
"""
from django.contrib import admin
from .models import Diagnosis, DiagnosticTest, ImagingStudy


@admin.register(Diagnosis)
class DiagnosisAdmin(admin.ModelAdmin):
    """
    Diagnosis admin.
    """
    list_display = [
        'patient', 'diagnosis_name', 'diagnosis_type', 'severity',
        'doctor', 'is_active', 'created_at'
    ]
    list_filter = ['diagnosis_type', 'severity', 'is_active', 'doctor']
    search_fields = ['diagnosis_name', 'diagnosis_code', 'patient__patient_id']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(DiagnosticTest)
class DiagnosticTestAdmin(admin.ModelAdmin):
    """
    Diagnostic test admin.
    """
    list_display = [
        'patient', 'test_name', 'test_type', 'status',
        'ordered_by', 'ordered_date'
    ]
    list_filter = ['test_type', 'status', 'ordered_date']
    search_fields = ['test_name', 'patient__patient_id']


@admin.register(ImagingStudy)
class ImagingStudyAdmin(admin.ModelAdmin):
    """
    Imaging study admin.
    """
    list_display = [
        'patient', 'study_id', 'modality', 'body_part',
        'acquisition_date'
    ]
    list_filter = ['modality', 'acquisition_date']
    search_fields = ['study_id', 'patient__patient_id', 'body_part']
