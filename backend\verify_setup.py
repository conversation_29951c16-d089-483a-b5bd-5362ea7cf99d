#!/usr/bin/env python3
"""
Verification script for Quant-NEX Django Backend
Tests all components to ensure everything is working correctly.
"""

import os
import sys
import django
from django.core.management import execute_from_command_line

def setup_django():
    """Setup Django environment."""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'quantnex.settings')
    django.setup()

def test_imports():
    """Test that all models and apps can be imported."""
    print("🔍 Testing imports...")
    
    try:
        # Test authentication app
        from authentication.models import User, UserPreferences, UserSession
        from authentication.serializers import UserProfileSerializer
        from authentication.views import UserRegistrationView
        print("✅ Authentication app imports successful")
        
        # Test patients app
        from patients.models import Patient, MedicalRecord, Appointment
        from patients.serializers import PatientSerializer
        from patients.views import PatientViewSet
        print("✅ Patients app imports successful")
        
        # Test dashboard app
        from dashboard.models import DashboardWidget, SystemAlert
        from dashboard.serializers import DashboardStatsSerializer
        from dashboard.views import dashboard_overview
        print("✅ Dashboard app imports successful")
        
        # Test diagnoses app
        from diagnoses.models import Diagnosis, DiagnosticTest, ImagingStudy
        from diagnoses.serializers import DiagnosisSerializer
        from diagnoses.views import DiagnosisListCreateView
        print("✅ Diagnoses app imports successful")
        
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_database():
    """Test database connectivity and models."""
    print("\n🗄️ Testing database...")
    
    try:
        from django.db import connection
        from authentication.models import User
        
        # Test database connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            if result[0] == 1:
                print("✅ Database connection successful")
            
        # Test model queries
        user_count = User.objects.count()
        print(f"✅ User model query successful (found {user_count} users)")
        
        return True
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def test_urls():
    """Test URL configuration."""
    print("\n🔗 Testing URL configuration...")
    
    try:
        from django.urls import reverse
        from django.test import Client
        
        client = Client()
        
        # Test main URLs
        urls_to_test = [
            '/admin/',
            '/api/schema/',
            '/api/docs/',
        ]
        
        for url in urls_to_test:
            try:
                response = client.get(url)
                if response.status_code in [200, 301, 302, 401, 403]:
                    print(f"✅ URL {url} accessible (status: {response.status_code})")
                else:
                    print(f"⚠️ URL {url} returned status: {response.status_code}")
            except Exception as e:
                print(f"❌ URL {url} error: {e}")
        
        return True
    except Exception as e:
        print(f"❌ URL configuration error: {e}")
        return False

def test_api_endpoints():
    """Test API endpoints."""
    print("\n🌐 Testing API endpoints...")
    
    try:
        from django.test import Client
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        client = Client()
        
        # Create test user if doesn't exist
        if not User.objects.filter(username='testuser').exists():
            User.objects.create_user(
                username='testuser',
                email='<EMAIL>',
                password='testpass123'
            )
        
        # Test login endpoint
        login_response = client.post('/api/auth/login/', {
            'username': 'testuser',
            'password': 'testpass123'
        }, content_type='application/json')
        
        if login_response.status_code == 200:
            print("✅ Login endpoint working")
            
            # Get token for authenticated requests
            import json
            login_data = json.loads(login_response.content)
            token = login_data.get('access')
            
            if token:
                # Test authenticated endpoints
                auth_headers = {'HTTP_AUTHORIZATION': f'Bearer {token}'}
                
                endpoints_to_test = [
                    '/api/auth/profile/',
                    '/api/patients/',
                    '/api/dashboard/overview/',
                ]
                
                for endpoint in endpoints_to_test:
                    response = client.get(endpoint, **auth_headers)
                    if response.status_code == 200:
                        print(f"✅ Endpoint {endpoint} working")
                    else:
                        print(f"⚠️ Endpoint {endpoint} returned status: {response.status_code}")
        else:
            print(f"⚠️ Login endpoint returned status: {login_response.status_code}")
        
        return True
    except Exception as e:
        print(f"❌ API endpoint error: {e}")
        return False

def test_admin():
    """Test Django admin."""
    print("\n👤 Testing Django admin...")
    
    try:
        from django.contrib.admin import site
        from authentication.models import User
        from patients.models import Patient
        from dashboard.models import DashboardWidget
        
        # Check if models are registered in admin
        registered_models = [model._meta.model for model in site._registry.values()]
        
        models_to_check = [User, Patient, DashboardWidget]
        for model in models_to_check:
            if model in registered_models:
                print(f"✅ {model.__name__} registered in admin")
            else:
                print(f"⚠️ {model.__name__} not registered in admin")
        
        return True
    except Exception as e:
        print(f"❌ Admin test error: {e}")
        return False

def main():
    """Run all verification tests."""
    print("🏥 Quant-NEX Django Backend Verification")
    print("=" * 50)
    
    # Setup Django
    setup_django()
    
    # Run tests
    tests = [
        test_imports,
        test_database,
        test_urls,
        test_api_endpoints,
        test_admin,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your Django backend is ready to use.")
        print("\nNext steps:")
        print("1. Start the server: python manage.py runserver 8000")
        print("2. Access API docs: http://localhost:8000/api/docs/")
        print("3. Access admin: http://localhost:8000/admin/")
        print("4. Test with frontend at: http://localhost:3000")
    else:
        print("⚠️ Some tests failed. Please check the errors above.")
        print("You may need to run migrations or fix configuration issues.")

if __name__ == '__main__':
    main()
