import requests
import json

# Test Django API
base_url = "http://localhost:8000/api"

# Test login
login_data = {
    "username": "admin",
    "password": "quantnex123"
}

try:
    print("Testing Django API login...")
    response = requests.post(f"{base_url}/auth/login/", json=login_data)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.text}")
    
    if response.status_code == 200:
        data = response.json()
        access_token = data.get('access')
        print(f"Login successful! Access token: {access_token[:50]}...")
        
        # Test authenticated endpoint
        headers = {"Authorization": f"Bearer {access_token}"}
        dashboard_response = requests.get(f"{base_url}/dashboard/overview/", headers=headers)
        print(f"Dashboard Status: {dashboard_response.status_code}")
        print(f"Dashboard Response: {dashboard_response.text}")
        
except Exception as e:
    print(f"Error: {e}")
