#!/usr/bin/env python3
"""
Unified Startup Script for Quant-NEX Healthcare Application
Runs both Django backend and Next.js frontend in parallel on a single domain
"""

import subprocess
import threading
import time
import requests
import os
import signal
import sys
from pathlib import Path

class UnifiedServer:
    def __init__(self):
        self.django_process = None
        self.nextjs_process = None
        self.running = True
        
    def start_django_server(self):
        """Start Django backend server"""
        print("🚀 Starting Django Backend Server...")
        try:
            # Change to backend directory
            backend_dir = Path(__file__).parent / "backend"
            
            # Start Django development server
            self.django_process = subprocess.Popen(
                [sys.executable, "manage.py", "runserver", "localhost:8000"],
                cwd=backend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            # Monitor Django output
            for line in iter(self.django_process.stdout.readline, ''):
                if self.running:
                    print(f"[DJANGO] {line.strip()}")
                else:
                    break
                    
        except Exception as e:
            print(f"❌ Django server error: {e}")
    
    def start_nextjs_server(self):
        """Start Next.js frontend server"""
        print("🌐 Starting Next.js Frontend Server...")
        try:
            # Start Next.js development server
            self.nextjs_process = subprocess.Popen(
                ["npm", "run", "dev"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            # Monitor Next.js output
            for line in iter(self.nextjs_process.stdout.readline, ''):
                if self.running:
                    print(f"[NEXT.JS] {line.strip()}")
                else:
                    break
                    
        except Exception as e:
            print(f"❌ Next.js server error: {e}")
    
    def wait_for_servers(self):
        """Wait for both servers to be ready"""
        print("⏳ Waiting for servers to start...")
        
        # Wait for Django
        django_ready = False
        for i in range(30):  # 30 second timeout
            try:
                response = requests.get("http://localhost:8000/api/docs/", timeout=2)
                if response.status_code == 200:
                    print("✅ Django Backend: READY")
                    django_ready = True
                    break
            except:
                pass
            time.sleep(1)
        
        if not django_ready:
            print("❌ Django Backend: TIMEOUT")
            return False
        
        # Wait for Next.js
        nextjs_ready = False
        for i in range(30):  # 30 second timeout
            try:
                response = requests.get("http://localhost:3000", timeout=2)
                if response.status_code == 200:
                    print("✅ Next.js Frontend: READY")
                    nextjs_ready = True
                    break
            except:
                pass
            time.sleep(1)
        
        if not nextjs_ready:
            print("❌ Next.js Frontend: TIMEOUT")
            return False
        
        return True
    
    def test_integration(self):
        """Test the integration between frontend and backend"""
        print("\n🔧 Testing Integration...")
        
        # Test API proxy through Next.js
        try:
            # Test login through Next.js proxy
            response = requests.post("http://localhost:3000/api/auth/login/", 
                                   json={"username": "admin", "password": "quantnex123"},
                                   timeout=10)
            
            if response.status_code == 200:
                print("✅ API Proxy: WORKING")
                print("✅ Authentication: WORKING")
                return True
            else:
                print(f"❌ API Proxy: FAILED ({response.status_code})")
                return False
        except Exception as e:
            print(f"❌ Integration Test: ERROR - {e}")
            return False
    
    def start(self):
        """Start both servers in parallel"""
        print("🎯 QUANT-NEX UNIFIED SERVER STARTUP")
        print("=" * 50)
        print("Starting Django Backend and Next.js Frontend in parallel...")
        print("Single-page hosting with API proxy enabled")
        print()
        
        # Start Django in background thread
        django_thread = threading.Thread(target=self.start_django_server, daemon=True)
        django_thread.start()
        
        # Start Next.js in background thread  
        nextjs_thread = threading.Thread(target=self.start_nextjs_server, daemon=True)
        nextjs_thread.start()
        
        # Wait for servers to be ready
        if not self.wait_for_servers():
            print("❌ Server startup failed!")
            self.stop()
            return False
        
        # Test integration
        if not self.test_integration():
            print("⚠️ Integration test failed, but servers are running")
        
        print("\n" + "=" * 50)
        print("🎉 QUANT-NEX UNIFIED SERVER: READY!")
        print("=" * 50)
        print()
        print("📍 ACCESS POINTS:")
        print("   🌐 Main Application: http://localhost:3000")
        print("   🔐 Login Page:       http://localhost:3000/login")
        print("   📊 Dashboard:        http://localhost:3000/dashboard")
        print("   🔧 API Docs:         http://localhost:3000/api/docs/")
        print("   ⚙️  Admin Panel:      http://localhost:3000/admin/")
        print()
        print("🔑 TEST CREDENTIALS:")
        print("   Username: admin")
        print("   Password: quantnex123")
        print()
        print("✨ FEATURES AVAILABLE:")
        print("   • Single-page hosting (all on localhost:3000)")
        print("   • API proxy (backend accessible through frontend)")
        print("   • 3D medical visualizations")
        print("   • Real-time dashboard")
        print("   • Secure authentication")
        print("   • Complete healthcare management")
        print()
        print("Press Ctrl+C to stop all servers...")
        
        try:
            # Keep main thread alive
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Shutting down servers...")
            self.stop()
    
    def stop(self):
        """Stop both servers"""
        self.running = False
        
        if self.django_process:
            try:
                self.django_process.terminate()
                self.django_process.wait(timeout=5)
                print("✅ Django server stopped")
            except:
                self.django_process.kill()
                print("🔥 Django server force killed")
        
        if self.nextjs_process:
            try:
                self.nextjs_process.terminate()
                self.nextjs_process.wait(timeout=5)
                print("✅ Next.js server stopped")
            except:
                self.nextjs_process.kill()
                print("🔥 Next.js server force killed")

def main():
    """Main function"""
    server = UnifiedServer()
    
    # Handle Ctrl+C gracefully
    def signal_handler(sig, frame):
        print("\n🛑 Received shutdown signal...")
        server.stop()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Start unified server
    server.start()

if __name__ == '__main__':
    main()
