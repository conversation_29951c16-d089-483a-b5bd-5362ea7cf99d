#!/usr/bin/env python3
"""
Quick setup and run script for Quant-NEX Django Backend
"""

import os
import sys
import subprocess
import platform

def run_command(command, description=""):
    """Run a command and handle errors."""
    print(f"\n🔄 {description}")
    print(f"Running: {command}")
    
    try:
        if platform.system() == "Windows":
            result = subprocess.run(command, shell=True, check=True, text=True)
        else:
            result = subprocess.run(command.split(), check=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error in {description}: {e}")
        return False

def check_python():
    """Check Python version."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ is required")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} detected")
    return True

def setup_and_run():
    """Setup and run the Django backend."""
    print("🏥 Quant-NEX Django Backend Setup & Run")
    print("=" * 50)
    
    # Check Python version
    if not check_python():
        return
    
    # Check if we're in a virtual environment
    if not (hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)):
        print("⚠️  Warning: No virtual environment detected")
        print("It's recommended to use a virtual environment")
        response = input("Continue anyway? (y/N): ")
        if response.lower() != 'y':
            print("Setup cancelled. Please create a virtual environment first:")
            print("python -m venv venv")
            if platform.system() == "Windows":
                print("venv\\Scripts\\activate")
            else:
                print("source venv/bin/activate")
            return
    
    # Install dependencies
    if not run_command("pip install -r requirements.txt", "Installing dependencies"):
        print("Failed to install dependencies. Please check your pip installation.")
        return
    
    # Create .env file if it doesn't exist
    if not os.path.exists('.env'):
        print("\n📝 Creating .env file from template...")
        if os.path.exists('.env.example'):
            if platform.system() == "Windows":
                run_command("copy .env.example .env", "Creating .env file")
            else:
                run_command("cp .env.example .env", "Creating .env file")
        else:
            # Create a basic .env file
            with open('.env', 'w') as f:
                f.write("SECRET_KEY=django-insecure-development-key-change-in-production\n")
                f.write("DEBUG=True\n")
                f.write("ALLOWED_HOSTS=localhost,127.0.0.1\n")
            print("✅ Created basic .env file")
    
    # Run migrations
    if not run_command("python manage.py makemigrations", "Creating migrations"):
        print("Failed to create migrations")
        return
    
    if not run_command("python manage.py migrate", "Running migrations"):
        print("Failed to run migrations")
        return
    
    # Create superuser (optional)
    print("\n👤 Create superuser account?")
    create_superuser = input("Create superuser? (Y/n): ").lower()
    if create_superuser != 'n':
        print("Creating superuser account...")
        print("You can use these credentials for testing:")
        print("Username: admin")
        print("Email: <EMAIL>")
        print("Password: quantnex123")
        
        # Try to create superuser non-interactively
        env = os.environ.copy()
        env['DJANGO_SUPERUSER_USERNAME'] = 'admin'
        env['DJANGO_SUPERUSER_EMAIL'] = '<EMAIL>'
        env['DJANGO_SUPERUSER_PASSWORD'] = 'quantnex123'
        
        try:
            subprocess.run([
                sys.executable, 'manage.py', 'createsuperuser', '--noinput'
            ], env=env, check=True)
            print("✅ Superuser created successfully")
        except subprocess.CalledProcessError:
            print("⚠️  Superuser might already exist or creation failed")
    
    # Collect static files
    run_command("python manage.py collectstatic --noinput", "Collecting static files")
    
    print("\n🎉 Setup completed successfully!")
    print("\n🚀 Starting Django development server...")
    print("Backend will be available at: http://localhost:8000")
    print("API Documentation: http://localhost:8000/api/docs/")
    print("Admin Panel: http://localhost:8000/admin/")
    print("\nPress Ctrl+C to stop the server")
    
    # Start the development server
    try:
        subprocess.run([sys.executable, 'manage.py', 'runserver', '8000'], check=True)
    except KeyboardInterrupt:
        print("\n\n👋 Server stopped. Goodbye!")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Server failed to start: {e}")

if __name__ == '__main__':
    setup_and_run()
