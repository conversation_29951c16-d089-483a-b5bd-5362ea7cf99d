"use client"

import type React from "react"

import { createContext, useContext, useEffect, useState } from "react"

type User = {
  uid: string
  email: string | null
  displayName?: string | null
  photoURL?: string | null
  role?: string
} | null

type AuthContextType = {
  user: User
  loading: boolean
  login: (email: string, password: string) => Promise<User>
  register: (email: string, password: string, userData: any) => Promise<User>
  logout: () => Promise<void>
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  login: async () => null,
  register: async () => null,
  logout: async () => {},
})

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User>(null)
  const [loading, setLoading] = useState(true)

  // Mock user for demo purposes
  const mockUser = {
    uid: "mock-user-id",
    email: "<EMAIL>",
    displayName: "<PERSON><PERSON> <PERSON>",
    photoURL: null,
    role: "doctor",
  }

  useEffect(() => {
    // For demo purposes, set a mock user after a delay
    const timer = setTimeout(() => {
      setUser(mockUser)
      setLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  const login = async (email: string, password: string) => {
    try {
      // In a real app, this would call Firebase auth
      // const user = await authService.login(email, password)

      // For demo purposes, just return the mock user
      setUser(mockUser)
      return mockUser
    } catch (error) {
      console.error("Login error:", error)
      return null
    }
  }

  const register = async (email: string, password: string, userData: any) => {
    try {
      // In a real app, this would call Firebase auth
      // const user = await authService.register(email, password, userData)

      // For demo purposes, just return the mock user
      const newUser = {
        ...mockUser,
        email,
        displayName: `${userData.firstName} ${userData.lastName}`,
        role: userData.role,
      }
      setUser(newUser)
      return newUser
    } catch (error) {
      console.error("Registration error:", error)
      return null
    }
  }

  const logout = async () => {
    try {
      // In a real app, this would call Firebase auth
      // await authService.logout()

      // For demo purposes, just set user to null
      setUser(null)
    } catch (error) {
      console.error("Logout error:", error)
    }
  }

  return <AuthContext.Provider value={{ user, loading, login, register, logout }}>{children}</AuthContext.Provider>
}

export const useAuth = () => useContext(AuthContext)
