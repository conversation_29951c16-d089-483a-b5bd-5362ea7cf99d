"use client"

import type React from "react"

import { createContext, useContext, useEffect, useState } from "react"
import { authService } from "@/lib/django-api"

type User = {
  id: number
  username: string
  email: string | null
  display_name?: string | null
  full_name?: string | null
  role?: string
  specialization?: string
  is_verified?: boolean
} | null

type AuthContextType = {
  user: User
  loading: boolean
  login: (email: string, password: string) => Promise<User>
  register: (email: string, password: string, userData: any) => Promise<User>
  logout: () => Promise<void>
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  login: async () => null,
  register: async () => null,
  logout: async () => {},
})

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User>(null)
  const [loading, setLoading] = useState(true)

  // Mock user for demo purposes
  const mockUser = {
    uid: "mock-user-id",
    email: "<EMAIL>",
    displayName: "<PERSON><PERSON> <PERSON><PERSON>",
    photoURL: null,
    role: "doctor",
  }

  useEffect(() => {
    // Check if user is already logged in (has token)
    const checkAuth = async () => {
      try {
        const token = localStorage.getItem('access_token')
        if (token) {
          // Try to get user profile
          const userProfile = await authService.getProfile()
          setUser(userProfile)
        }
      } catch (error) {
        console.error("Auth check error:", error)
        // Clear invalid token
        localStorage.removeItem('access_token')
        localStorage.removeItem('refresh_token')
      } finally {
        setLoading(false)
      }
    }

    checkAuth()
  }, [])

  const login = async (email: string, password: string) => {
    try {
      setLoading(true)
      // Use Django API for authentication
      const response = await authService.login(email, password)
      const user = response.user
      setUser(user)
      setLoading(false)
      return user
    } catch (error) {
      console.error("Login error:", error)
      setLoading(false)
      return null
    }
  }

  const register = async (email: string, password: string, userData: any) => {
    try {
      setLoading(true)
      // Use Django API for registration
      const response = await authService.register(email, password, userData)
      const user = response.user
      setUser(user)
      setLoading(false)
      return user
    } catch (error) {
      console.error("Registration error:", error)
      setLoading(false)
      return null
    }
  }

  const logout = async () => {
    try {
      // Use Django API for logout
      await authService.logout()
      setUser(null)
    } catch (error) {
      console.error("Logout error:", error)
      setUser(null) // Still logout on error
    }
  }

  return <AuthContext.Provider value={{ user, loading, login, register, logout }}>{children}</AuthContext.Provider>
}

export const useAuth = () => useContext(AuthContext)
