import requests
import json

# Test login with proper headers
try:
    print("Testing login endpoint...")
    
    url = "http://localhost:8000/api/auth/login/"
    data = {
        "username": "admin",
        "password": "quantnex123"
    }
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    print(f"URL: {url}")
    print(f"Data: {data}")
    print(f"Headers: {headers}")
    
    response = requests.post(url, json=data, headers=headers)
    
    print(f"\nResponse Status: {response.status_code}")
    print(f"Response Headers: {dict(response.headers)}")
    print(f"Response Content: {response.text[:500]}...")
    
    if response.status_code == 200:
        try:
            json_data = response.json()
            print(f"JSON Response: {json_data}")
        except:
            print("Could not parse JSON response")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
