# Quant-NEX Healthcare API Documentation

## Overview

The Quant-NEX Healthcare API provides comprehensive endpoints for managing patients, medical records, appointments, and healthcare analytics. This RESTful API is built with Django REST Framework and supports JWT authentication.

## Base URL

- **Development**: `http://localhost:8000/api/`
- **Production**: `https://your-domain.com/api/`

## Authentication

All API endpoints require authentication using JWT tokens.

### Login
```http
POST /api/auth/login/
Content-Type: application/json

{
  "username": "dr.priya",
  "password": "quantnex123"
}
```

**Response:**
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user": {
    "id": 1,
    "username": "dr.priya",
    "display_name": "<PERSON><PERSON> <PERSON><PERSON>",
    "role": "doctor",
    "specialization": "oncology"
  }
}
```

### Using JWT Token
Include the access token in the Authorization header:
```http
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

## API Endpoints

### Authentication Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/auth/register/` | Register new user |
| POST | `/auth/login/` | User login |
| POST | `/auth/logout/` | User logout |
| GET | `/auth/profile/` | Get user profile |
| PUT | `/auth/profile/` | Update user profile |
| GET | `/auth/preferences/` | Get user preferences |
| PUT | `/auth/preferences/` | Update user preferences |
| POST | `/auth/change-password/` | Change password |

### Patient Management

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/patients/` | List patients |
| POST | `/patients/` | Create patient |
| GET | `/patients/{id}/` | Get patient details |
| PUT | `/patients/{id}/` | Update patient |
| DELETE | `/patients/{id}/` | Delete patient |
| GET | `/patients/search/` | Search patients |
| GET | `/patients/stats/` | Patient statistics |

### Medical Records

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/patients/{id}/records/` | Get patient records |
| POST | `/patients/{id}/records/` | Create medical record |
| GET | `/patients/records/{id}/` | Get record details |
| PUT | `/patients/records/{id}/` | Update record |
| DELETE | `/patients/records/{id}/` | Delete record |

### Appointments

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/patients/appointments/` | List appointments |
| POST | `/patients/appointments/` | Create appointment |
| GET | `/patients/appointments/{id}/` | Get appointment details |
| PUT | `/patients/appointments/{id}/` | Update appointment |
| DELETE | `/patients/appointments/{id}/` | Delete appointment |

### Dashboard Analytics

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/dashboard/overview/` | Dashboard overview |
| GET | `/dashboard/patient-stats/` | Patient statistics |
| GET | `/dashboard/appointment-analytics/` | Appointment analytics |
| GET | `/dashboard/system-status/` | System status |

### Diagnoses

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/diagnoses/` | List diagnoses |
| POST | `/diagnoses/` | Create diagnosis |
| GET | `/diagnoses/{id}/` | Get diagnosis details |
| PUT | `/diagnoses/{id}/` | Update diagnosis |
| GET | `/diagnoses/patient/{id}/` | Get patient diagnoses |

## Data Models

### Patient Model
```json
{
  "id": 1,
  "patient_id": "P-2024-0001",
  "first_name": "Arjun",
  "last_name": "Patel",
  "full_name": "Arjun Patel",
  "date_of_birth": "1967-03-15",
  "age": 57,
  "gender": "M",
  "blood_group": "B+",
  "phone_number": "+91-**********",
  "email": "<EMAIL>",
  "address": "123 MG Road, Mumbai",
  "city": "Mumbai",
  "state": "Maharashtra",
  "postal_code": "400001",
  "country": "India",
  "emergency_contact_name": "Sunita Patel",
  "emergency_contact_phone": "+91-**********",
  "emergency_contact_relation": "Wife",
  "height": 175.0,
  "weight": 70.0,
  "bmi": 22.86,
  "allergies": "Penicillin",
  "medical_history": "Hypertension, Diabetes",
  "current_medications": "Metformin, Lisinopril",
  "cancer_type": "Lung Carcinoma",
  "cancer_stage": "III",
  "diagnosis_date": "2024-01-15",
  "status": "active",
  "treatment_progress": 42,
  "assigned_doctor": 1,
  "assigned_doctor_name": "Dr. Priya Sharma",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-20T14:45:00Z",
  "last_visit": "2024-01-20T09:00:00Z"
}
```

### Medical Record Model
```json
{
  "id": 1,
  "patient": 1,
  "patient_name": "Arjun Patel",
  "doctor": 1,
  "doctor_name": "Dr. Priya Sharma",
  "record_type": "consultation",
  "title": "Follow-up Consultation",
  "description": "Patient showing improvement in symptoms",
  "notes": "Continue current treatment plan",
  "temperature": 98.6,
  "blood_pressure_systolic": 120,
  "blood_pressure_diastolic": 80,
  "heart_rate": 72,
  "respiratory_rate": 16,
  "oxygen_saturation": 98.0,
  "attachments": ["report1.pdf", "xray1.jpg"],
  "created_at": "2024-01-20T14:45:00Z",
  "updated_at": "2024-01-20T14:45:00Z"
}
```

### Appointment Model
```json
{
  "id": 1,
  "patient": 1,
  "patient_name": "Arjun Patel",
  "doctor": 1,
  "doctor_name": "Dr. Priya Sharma",
  "appointment_type": "follow_up",
  "title": "Follow-up Consultation",
  "description": "Review treatment progress",
  "scheduled_date": "2024-01-25",
  "scheduled_time": "10:00:00",
  "duration": 30,
  "status": "scheduled",
  "notes": "",
  "send_reminder": true,
  "reminder_sent": false,
  "created_at": "2024-01-20T15:00:00Z",
  "updated_at": "2024-01-20T15:00:00Z"
}
```

## Query Parameters

### Patient List
- `search`: Search by name, ID, phone, or email
- `status`: Filter by patient status (active, critical, remission, etc.)
- `assigned_doctor`: Filter by assigned doctor ID
- `page`: Page number for pagination
- `page_size`: Number of results per page

### Appointment List
- `date_from`: Filter appointments from date (YYYY-MM-DD)
- `date_to`: Filter appointments to date (YYYY-MM-DD)
- `status`: Filter by appointment status
- `patient`: Filter by patient ID

## Error Responses

### 400 Bad Request
```json
{
  "error": "Invalid input data",
  "details": {
    "field_name": ["This field is required."]
  }
}
```

### 401 Unauthorized
```json
{
  "detail": "Authentication credentials were not provided."
}
```

### 403 Forbidden
```json
{
  "detail": "You do not have permission to perform this action."
}
```

### 404 Not Found
```json
{
  "detail": "Not found."
}
```

## Rate Limiting

API requests are limited to 1000 requests per hour per user. Rate limit headers are included in responses:

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: **********
```

## Pagination

List endpoints use cursor-based pagination:

```json
{
  "count": 150,
  "next": "http://localhost:8000/api/patients/?page=2",
  "previous": null,
  "results": [...]
}
```

## Status Codes

- `200 OK`: Successful GET, PUT, PATCH
- `201 Created`: Successful POST
- `204 No Content`: Successful DELETE
- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Permission denied
- `404 Not Found`: Resource not found
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error

## Interactive Documentation

Visit the interactive API documentation:
- **Swagger UI**: `http://localhost:8000/api/docs/`
- **ReDoc**: `http://localhost:8000/api/redoc/`
