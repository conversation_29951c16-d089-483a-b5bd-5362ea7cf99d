Watching for file changes with StatReloader
"GET /api/docs/ HTTP/1.1" 200 4657
- Broken pipe from ('127.0.0.1', 53445)
Bad Request: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 400 76
- Broken pipe from ('127.0.0.1', 53461)
Bad Request: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 400 76
- Broken pipe from ('127.0.0.1', 53468)
Internal Server Error: /api/auth/login/
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\authentication\views.py", line 77, in post
    user.preferences
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\fields\related_descriptors.py", line 524, in __get__
    raise self.RelatedObjectDoesNotExist(
    ...<2 lines>...
    )
authentication.models.User.preferences.RelatedObjectDoesNotExist: User has no preferences.
"POST /api/auth/login/ HTTP/1.1" 500 106350
Internal Server Error: /api/auth/login/
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.IntegrityError: UNIQUE constraint failed: user_sessions.session_key

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\authentication\views.py", line 62, in post
    UserSession.objects.create(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^
        user=user,
        ^^^^^^^^^^
    ...<2 lines>...
        user_agent=request.META.get('HTTP_USER_AGENT', '')
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\query.py", line 677, in create
    obj.save(force_insert=True, using=self.db)
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\base.py", line 822, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\base.py", line 909, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\base.py", line 1067, in _save_table
    results = self._do_insert(
        cls._base_manager, using, fields, returning_fields, raw
    )
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\base.py", line 1108, in _do_insert
    return manager._insert(
           ~~~~~~~~~~~~~~~^
        [self],
        ^^^^^^^
    ...<3 lines>...
        raw=raw,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\query.py", line 1845, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1823, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.IntegrityError: UNIQUE constraint failed: user_sessions.session_key
"POST /api/auth/login/ HTTP/1.1" 500 194656
"GET /api/docs/ HTTP/1.1" 200 4657
"GET /api/schema/ HTTP/1.1" 200 185136
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 4162
"GET /static/admin/css/base.css HTTP/1.1" 200 21544
"GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2682
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2810
"GET /static/admin/css/login.css HTTP/1.1" 200 958
"GET /static/admin/css/responsive.css HTTP/1.1" 200 17905
"GET /static/admin/js/theme.js HTTP/1.1" 200 1943
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 3654
"POST /admin/login/?next=/admin/ HTTP/1.1" 200 4324
"GET /api/docs/ HTTP/1.1" 200 4657
Internal Server Error: /api/auth/login/
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.IntegrityError: UNIQUE constraint failed: user_sessions.session_key

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\authentication\views.py", line 62, in post
    UserSession.objects.create(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^
        user=user,
        ^^^^^^^^^^
    ...<2 lines>...
        user_agent=request.META.get('HTTP_USER_AGENT', '')
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\query.py", line 677, in create
    obj.save(force_insert=True, using=self.db)
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\base.py", line 822, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\base.py", line 909, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\base.py", line 1067, in _save_table
    results = self._do_insert(
        cls._base_manager, using, fields, returning_fields, raw
    )
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\base.py", line 1108, in _do_insert
    return manager._insert(
           ~~~~~~~~~~~~~~~^
        [self],
        ^^^^^^^
    ...<3 lines>...
        raw=raw,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\query.py", line 1845, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1823, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.IntegrityError: UNIQUE constraint failed: user_sessions.session_key
"POST /api/auth/login/ HTTP/1.1" 500 194656
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"GET /api/schema/ HTTP/1.1" 200 91064
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 4162
Method Not Allowed: /api/auth/login/
"GET /api/auth/login/ HTTP/1.1" 405 40
Internal Server Error: /api/auth/login/
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.IntegrityError: UNIQUE constraint failed: user_sessions.session_key

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\authentication\views.py", line 62, in post
    UserSession.objects.create(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^
        user=user,
        ^^^^^^^^^^
    ...<2 lines>...
        user_agent=request.META.get('HTTP_USER_AGENT', '')
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\query.py", line 677, in create
    obj.save(force_insert=True, using=self.db)
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\base.py", line 822, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\base.py", line 909, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\base.py", line 1067, in _save_table
    results = self._do_insert(
        cls._base_manager, using, fields, returning_fields, raw
    )
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\base.py", line 1108, in _do_insert
    return manager._insert(
           ~~~~~~~~~~~~~~~^
        [self],
        ^^^^^^^
    ...<3 lines>...
        raw=raw,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\query.py", line 1845, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1823, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.IntegrityError: UNIQUE constraint failed: user_sessions.session_key
"POST /api/auth/login/ HTTP/1.1" 500 27766
Watching for file changes with StatReloader
Internal Server Error: /api/auth/login/
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.IntegrityError: UNIQUE constraint failed: user_sessions.session_key

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\authentication\views.py", line 62, in post
    UserSession.objects.create(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^
        user=user,
        ^^^^^^^^^^
    ...<2 lines>...
        user_agent=request.META.get('HTTP_USER_AGENT', '')
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\query.py", line 677, in create
    obj.save(force_insert=True, using=self.db)
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\base.py", line 822, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\base.py", line 909, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\base.py", line 1067, in _save_table
    results = self._do_insert(
        cls._base_manager, using, fields, returning_fields, raw
    )
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\base.py", line 1108, in _do_insert
    return manager._insert(
           ~~~~~~~~~~~~~~~^
        [self],
        ^^^^^^^
    ...<3 lines>...
        raw=raw,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\query.py", line 1845, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1823, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.IntegrityError: UNIQUE constraint failed: user_sessions.session_key
"POST /api/auth/login/ HTTP/1.1" 500 27766
C:\Users\<USER>\Downloads\quant-nex\backend\authentication\views.py changed, reloading.
Watching for file changes with StatReloader
Internal Server Error: /api/auth/login/
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\authentication\views.py", line 81, in post
    user.preferences
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\fields\related_descriptors.py", line 524, in __get__
    raise self.RelatedObjectDoesNotExist(
    ...<2 lines>...
    )
authentication.models.User.preferences.RelatedObjectDoesNotExist: User has no preferences.
"POST /api/auth/login/ HTTP/1.1" 500 24029
C:\Users\<USER>\Downloads\quant-nex\backend\authentication\views.py changed, reloading.
Watching for file changes with StatReloader
"POST /api/auth/login/ HTTP/1.1" 200 1235
"GET /api/docs/ HTTP/1.1" 200 4657
"POST /api/auth/login/ HTTP/1.1" 200 1235
Internal Server Error: /api/dashboard/overview/
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\dashboard\views.py", line 54, in dashboard_overview
    'upcoming_appointments': Appointment.objects.filter(
                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~
        scheduled_date__gt=today,
        ~~~~~~~~~~~~~~~~~~~~~~~~~
        doctor=user if user.role == 'doctor' else None
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    ).count()[:5],
    ~~~~~~~~~^^^^
TypeError: 'int' object is not subscriptable
"GET /api/dashboard/overview/ HTTP/1.1" 500 105938
"GET /api/patients/ HTTP/1.1" 200 52
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 1235
Internal Server Error: /api/dashboard/overview/
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\dashboard\views.py", line 54, in dashboard_overview
    'upcoming_appointments': Appointment.objects.filter(
                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~
        scheduled_date__gt=today,
        ~~~~~~~~~~~~~~~~~~~~~~~~~
        doctor=user if user.role == 'doctor' else None
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    ).count()[:5],
    ~~~~~~~~~^^^^
TypeError: 'int' object is not subscriptable
"GET /api/dashboard/overview/ HTTP/1.1" 500 105938
"GET /api/patients/ HTTP/1.1" 200 52
Not Found: /api/patients/stats/
"GET /api/patients/stats/ HTTP/1.1" 404 23
"GET /api/auth/profile/ HTTP/1.1" 200 446
"GET /api/docs/ HTTP/1.1" 200 4657
"GET /api/schema/ HTTP/1.1" 200 91064
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 4162
"GET /api/docs/ HTTP/1.1" 200 4657
"POST /api/auth/login/ HTTP/1.1" 200 1235
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
Unauthorized: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 401 63
"POST /api/auth/login/ HTTP/1.1" 200 1235
Bad Request: /api/auth/register/
"POST /api/auth/register/ HTTP/1.1" 400 48
"GET /api/auth/profile/ HTTP/1.1" 200 446
Internal Server Error: /api/dashboard/overview/
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\dashboard\views.py", line 54, in dashboard_overview
    'upcoming_appointments': Appointment.objects.filter(
                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~
        scheduled_date__gt=today,
        ~~~~~~~~~~~~~~~~~~~~~~~~~
        doctor=user if user.role == 'doctor' else None
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    ).count()[:5],
    ~~~~~~~~~^^^^
TypeError: 'int' object is not subscriptable
"GET /api/dashboard/overview/ HTTP/1.1" 500 105938
"POST /api/auth/login/ HTTP/1.1" 200 1235
"GET /api/auth/profile/ HTTP/1.1" 200 446
Internal Server Error: /api/dashboard/overview/
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\dashboard\views.py", line 54, in dashboard_overview
    'upcoming_appointments': Appointment.objects.filter(
                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~
        scheduled_date__gt=today,
        ~~~~~~~~~~~~~~~~~~~~~~~~~
        doctor=user if user.role == 'doctor' else None
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    ).count()[:5],
    ~~~~~~~~~^^^^
TypeError: 'int' object is not subscriptable
"GET /api/dashboard/overview/ HTTP/1.1" 500 105938
Not Found: /api/patients/stats/
"GET /api/patients/stats/ HTTP/1.1" 404 23
"GET /api/auth/profile/ HTTP/1.1" 200 446
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 1235
Internal Server Error: /api/dashboard/overview/
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\dashboard\views.py", line 54, in dashboard_overview
    'upcoming_appointments': Appointment.objects.filter(
                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~
        scheduled_date__gt=today,
        ~~~~~~~~~~~~~~~~~~~~~~~~~
        doctor=user if user.role == 'doctor' else None
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    ).count()[:5],
    ~~~~~~~~~^^^^
TypeError: 'int' object is not subscriptable
"GET /api/dashboard/overview/ HTTP/1.1" 500 105938
"GET /api/dashboard/patient-stats/ HTTP/1.1" 200 248
"GET /api/dashboard/appointment-analytics/ HTTP/1.1" 200 145
"GET /api/dashboard/system-status/ HTTP/1.1" 200 169
Internal Server Error: /api/dashboard/alerts/
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\generics.py", line 199, in get
    return self.list(request, *args, **kwargs)
           ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\generics.py", line 171, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\pagination.py", line 204, in paginate_queryset
    self.page = paginator.page(page_number)
                ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\paginator.py", line 89, in page
    number = self.validate_number(number)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\paginator.py", line 70, in validate_number
    if number > self.num_pages:
                ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\paginator.py", line 116, in num_pages
    if self.count == 0 and not self.allow_empty_first_page:
       ^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\paginator.py", line 110, in count
    return c()
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\query.py", line 618, in count
    return self.query.get_count(using=self.db)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\sql\query.py", line 618, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\sql\query.py", line 604, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1549, in execute_sql
    sql, params = self.as_sql()
                  ~~~~~~~~~~~^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 2087, in as_sql
    ).as_sql(with_col_aliases=True)
      ~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 764, in as_sql
    self.compile(self.where) if self.where is not None else ("", [])
    ~~~~~~~~~~~~^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 546, in compile
    sql, params = node.as_sql(self, self.connection)
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\sql\where.py", line 150, in as_sql
    sql, params = compiler.compile(child)
                  ~~~~~~~~~~~~~~~~^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 546, in compile
    sql, params = node.as_sql(self, self.connection)
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\sql\where.py", line 150, in as_sql
    sql, params = compiler.compile(child)
                  ~~~~~~~~~~~~~~~~^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 546, in compile
    sql, params = node.as_sql(self, self.connection)
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\fields\json.py", line 188, in as_sql
    raise NotSupportedError(
        "contains lookup is not supported on this database backend."
    )
django.db.utils.NotSupportedError: contains lookup is not supported on this database backend.
"GET /api/dashboard/alerts/ HTTP/1.1" 500 200444
C:\Users\<USER>\Downloads\quant-nex\backend\dashboard\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Downloads\quant-nex\backend\dashboard\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Downloads\quant-nex\backend\dashboard\views.py changed, reloading.
Watching for file changes with StatReloader
"POST /api/auth/login/ HTTP/1.1" 200 1235
"GET /api/dashboard/overview/ HTTP/1.1" 200 400
"GET /api/dashboard/patient-stats/ HTTP/1.1" 200 248
"GET /api/dashboard/appointment-analytics/ HTTP/1.1" 200 145
"GET /api/dashboard/system-status/ HTTP/1.1" 200 169
"GET /api/dashboard/alerts/ HTTP/1.1" 200 52
"POST /api/auth/login/ HTTP/1.1" 200 1235
"GET /api/dashboard/overview/ HTTP/1.1" 200 400
"POST /api/auth/login/ HTTP/1.1" 200 1235
"GET /api/auth/profile/ HTTP/1.1" 200 446
"GET /api/dashboard/overview/ HTTP/1.1" 200 400
Not Found: /api/patients/stats/
"GET /api/patients/stats/ HTTP/1.1" 404 23
"GET /api/auth/profile/ HTTP/1.1" 200 446
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"GET /api/docs/ HTTP/1.1" 200 4657
"POST /api/auth/login/ HTTP/1.1" 200 1235
"GET /api/auth/profile/ HTTP/1.1" 200 446
"GET /api/dashboard/overview/ HTTP/1.1" 200 400
"GET /api/dashboard/patient-stats/ HTTP/1.1" 200 248
"GET /api/dashboard/system-status/ HTTP/1.1" 200 169
"GET /api/auth/profile/ HTTP/1.1" 200 446
"GET /api/patients/ HTTP/1.1" 200 52
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"GET /api/docs/ HTTP/1.1" 200 4657
"GET /api/docs/ HTTP/1.1" 200 4657
"POST /api/auth/login/ HTTP/1.1" 200 1235
"POST /api/auth/login/ HTTP/1.1" 200 1235
"GET /api/auth/profile/ HTTP/1.1" 200 446
"GET /api/dashboard/overview/ HTTP/1.1" 200 400
Not Found: /api/patients/stats/
"GET /api/patients/stats/ HTTP/1.1" 404 23
"GET /api/auth/profile/ HTTP/1.1" 200 446
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"GET /api/docs/ HTTP/1.1" 200 4657
"POST /api/auth/login/ HTTP/1.1" 200 1235
"GET /api/docs/ HTTP/1.1" 200 4657
"POST /api/auth/login/ HTTP/1.1" 200 1235
Unauthorized: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 401 63
"OPTIONS /api/auth/register/ HTTP/1.1" 200 0
Internal Server Error: /api/auth/register/
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.IntegrityError: UNIQUE constraint failed: auth_user.license_number

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\generics.py", line 190, in post
    return self.create(request, *args, **kwargs)
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\authentication\views.py", line 33, in create
    user = serializer.save()
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 212, in save
    self.instance = self.create(validated_data)
                    ~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\authentication\serializers.py", line 34, in create
    user = User.objects.create_user(**validated_data)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\contrib\auth\models.py", line 161, in create_user
    return self._create_user(username, email, password, **extra_fields)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\contrib\auth\models.py", line 155, in _create_user
    user.save(using=self._db)
    ~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\contrib\auth\base_user.py", line 77, in save
    super().save(*args, **kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\base.py", line 822, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\base.py", line 909, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\base.py", line 1067, in _save_table
    results = self._do_insert(
        cls._base_manager, using, fields, returning_fields, raw
    )
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\base.py", line 1108, in _do_insert
    return manager._insert(
           ~~~~~~~~~~~~~~~^
        [self],
        ^^^^^^^
    ...<3 lines>...
        raw=raw,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\query.py", line 1845, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1823, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.IntegrityError: UNIQUE constraint failed: auth_user.license_number
"POST /api/auth/register/ HTTP/1.1" 500 227235
"POST /api/auth/login/ HTTP/1.1" 200 1235
"GET /api/docs/ HTTP/1.1" 200 4657
"POST /api/auth/login/ HTTP/1.1" 200 1235
"POST /api/auth/login/ HTTP/1.1" 200 1235
Internal Server Error: /api/auth/register/
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.IntegrityError: UNIQUE constraint failed: auth_user.license_number

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\generics.py", line 190, in post
    return self.create(request, *args, **kwargs)
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\authentication\views.py", line 33, in create
    user = serializer.save()
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\rest_framework\serializers.py", line 212, in save
    self.instance = self.create(validated_data)
                    ~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\authentication\serializers.py", line 34, in create
    user = User.objects.create_user(**validated_data)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\contrib\auth\models.py", line 161, in create_user
    return self._create_user(username, email, password, **extra_fields)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\contrib\auth\models.py", line 155, in _create_user
    user.save(using=self._db)
    ~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\contrib\auth\base_user.py", line 77, in save
    super().save(*args, **kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\base.py", line 822, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\base.py", line 909, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\base.py", line 1067, in _save_table
    results = self._do_insert(
        cls._base_manager, using, fields, returning_fields, raw
    )
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\base.py", line 1108, in _do_insert
    return manager._insert(
           ~~~~~~~~~~~~~~~^
        [self],
        ^^^^^^^
    ...<3 lines>...
        raw=raw,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\query.py", line 1845, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1823, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.IntegrityError: UNIQUE constraint failed: auth_user.license_number
"POST /api/auth/register/ HTTP/1.1" 500 225394
"GET /api/docs/ HTTP/1.1" 200 4657
"POST /api/auth/login/ HTTP/1.1" 200 1235
"GET /api/auth/profile/ HTTP/1.1" 200 446
"POST /api/auth/login/ HTTP/1.1" 200 1235
"GET /api/dashboard/overview/ HTTP/1.1" 200 400
"GET /api/docs/ HTTP/1.1" 200 4657
"POST /api/auth/login/ HTTP/1.1" 200 1235
Unauthorized: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 401 63
Unauthorized: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 401 63
"POST /api/auth/login/ HTTP/1.1" 200 1235
"POST /api/auth/login/ HTTP/1.1" 200 1235
Internal Server Error: /api/auth/login
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\utils\deprecation.py", line 136, in __call__
    response = self.process_response(request, response)
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\middleware\common.py", line 108, in process_response
    return self.response_redirect_class(self.get_full_path_with_slash(request))
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Downloads\quant-nex\backend\venv\Lib\site-packages\django\middleware\common.py", line 87, in get_full_path_with_slash
    raise RuntimeError(
    ...<9 lines>...
    )
RuntimeError: You called this URL via POST, but the URL doesn't end in a slash and you have APPEND_SLASH set. Django can't redirect to the slash URL while maintaining POST data. Change your form to point to localhost:8000/api/auth/login/ (note the trailing slash), or set APPEND_SLASH=False in your Django settings.
"POST /api/auth/login HTTP/1.1" 500 78259
"GET /api/docs/ HTTP/1.1" 200 4657
"POST /api/auth/login/ HTTP/1.1" 200 1235
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
Unauthorized: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 401 63
