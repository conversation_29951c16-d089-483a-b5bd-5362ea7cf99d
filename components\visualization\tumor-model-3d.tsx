"use client"

import { useRef, useState, useCallback } from "react"
import { <PERSON><PERSON>, use<PERSON><PERSON><PERSON>, useThree } from "@react-three/fiber"
import { OrbitControls, Environment, Html, Text, PerspectiveCamera, ContactShadows } from "@react-three/drei"
import { <PERSON><PERSON><PERSON> } from "@/components/ui/slider"
import { But<PERSON> } from "@/components/ui/button"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Eye,
  EyeOff,
  Maximize2,
  Minimize2,
  RotateCcw,
  ZoomIn,
  ZoomOut,
  Move3D,
  RotateCw,
  Target,
  Layers,
  Activity,
  Info,
  Play,
  Pause,
  Clock,
  Slice,
  Lightbulb,
  TrendingUp
} from "lucide-react"
import * as THREE from "three"

// Export as TumorVisualization3D to match the import in other files
export function TumorVisualization3D() {
  const [opacity, setOpacity] = useState(0.7)
  const [showLabels, setShowLabels] = useState(true)
  const [zoom, setZoom] = useState(1)
  const [activeTab, setActiveTab] = useState("tumor")
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [autoRotate, setAutoRotate] = useState(false)
  const [rotationSpeed, setRotationSpeed] = useState(1)
  const [showWireframe, setShowWireframe] = useState(false)
  const [lightingIntensity, setLightingIntensity] = useState(1)
  const [selectedRegion, setSelectedRegion] = useState<string | null>(null)
  const [cameraPosition, setCameraPosition] = useState([0, 0, 5])
  const [showMeasurements, setShowMeasurements] = useState(false)

  // Enhanced 4D Features
  const [timeAnimation, setTimeAnimation] = useState(false)
  const [timeSpeed, setTimeSpeed] = useState(1)
  const [layerPeeling, setLayerPeeling] = useState(false)
  const [crossSectionMode, setCrossSectionMode] = useState(false)
  const [crossSectionPosition, setCrossSectionPosition] = useState(0)
  const [dynamicLighting, setDynamicLighting] = useState(true)
  const [bloodFlowAnimation, setBloodFlowAnimation] = useState(false)
  const [tumorGrowthSimulation, setTumorGrowthSimulation] = useState(false)

  const controlsRef = useRef<any>(null)

  const resetCamera = useCallback(() => {
    if (controlsRef.current) {
      controlsRef.current.reset()
    }
    setCameraPosition([0, 0, 5])
    setZoom(1)
  }, [])

  const zoomIn = useCallback(() => {
    setZoom(prev => Math.min(prev + 0.2, 3))
  }, [])

  const zoomOut = useCallback(() => {
    setZoom(prev => Math.max(prev - 0.2, 0.5))
  }, [])

  return (
    <div className={`${isFullscreen ? 'fixed inset-0 z-50 bg-black' : 'w-full h-[500px]'} rounded-xl overflow-hidden border border-blue-500/20 bg-gradient-to-br from-gray-900/50 to-blue-900/20 backdrop-blur-sm`}>
      <div className="relative w-full h-full">
        <Canvas
          camera={{ position: cameraPosition as [number, number, number], fov: 45 }}
          shadows
          gl={{ antialias: true, alpha: true }}
        >
          <PerspectiveCamera makeDefault position={cameraPosition as [number, number, number]} fov={45} />

          {/* Enhanced Lighting Setup */}
          <ambientLight intensity={0.3 * lightingIntensity} />
          <directionalLight
            position={[10, 10, 10]}
            intensity={0.8 * lightingIntensity}
            castShadow
            shadow-mapSize-width={2048}
            shadow-mapSize-height={2048}
          />
          <pointLight position={[-10, -10, -10]} intensity={0.4 * lightingIntensity} color="#4f46e5" />
          <spotLight
            position={[0, 10, 0]}
            intensity={0.6 * lightingIntensity}
            angle={0.3}
            penumbra={0.5}
            color="#06b6d4"
            castShadow
          />

          <Environment preset="studio" />
          <ContactShadows opacity={0.4} scale={10} blur={1} far={10} resolution={256} color="#000000" />

          {activeTab === "tumor" && (
            <TumorModel
              opacity={opacity}
              showLabels={showLabels}
              zoom={zoom}
              showWireframe={showWireframe}
              onRegionSelect={setSelectedRegion}
              showMeasurements={showMeasurements}
              timeAnimation={timeAnimation}
              timeSpeed={timeSpeed}
              layerPeeling={layerPeeling}
              crossSectionMode={crossSectionMode}
              crossSectionPosition={crossSectionPosition}
              bloodFlowAnimation={bloodFlowAnimation}
              tumorGrowthSimulation={tumorGrowthSimulation}
              lightingIntensity={lightingIntensity}
            />
          )}
          {activeTab === "brain" && (
            <BrainModel
              opacity={opacity}
              showLabels={showLabels}
              zoom={zoom}
              showWireframe={showWireframe}
              onRegionSelect={setSelectedRegion}
              showMeasurements={showMeasurements}
              timeAnimation={timeAnimation}
              timeSpeed={timeSpeed}
              layerPeeling={layerPeeling}
              crossSectionMode={crossSectionMode}
              crossSectionPosition={crossSectionPosition}
              bloodFlowAnimation={bloodFlowAnimation}
              tumorGrowthSimulation={tumorGrowthSimulation}
              lightingIntensity={lightingIntensity}
            />
          )}

          <OrbitControls
            ref={controlsRef}
            enablePan={true}
            enableZoom={true}
            enableRotate={true}
            autoRotate={autoRotate}
            autoRotateSpeed={rotationSpeed}
            enableDamping={true}
            dampingFactor={0.05}
            minDistance={2}
            maxDistance={20}
            maxPolarAngle={Math.PI}
            minPolarAngle={0}
          />
        </Canvas>

        {/* Enhanced Control Panel */}
        <div className="absolute top-4 right-4 bg-gray-900/90 backdrop-blur-sm rounded-lg p-4 z-10 min-w-[280px] border border-blue-500/20">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-semibold text-white flex items-center gap-2">
              <Activity className="h-4 w-4 text-medical-blue" />
              3D Controls
            </h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsFullscreen(!isFullscreen)}
              className="text-gray-400 hover:text-white"
            >
              {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
            </Button>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mb-4">
            <TabsList className="grid grid-cols-2 bg-gray-800">
              <TabsTrigger value="tumor" className="text-xs">Tumor View</TabsTrigger>
              <TabsTrigger value="brain" className="text-xs">Brain View</TabsTrigger>
            </TabsList>
          </Tabs>

          <div className="space-y-4">
            {/* Opacity Control */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs text-gray-300 flex items-center gap-1">
                  <Eye className="h-3 w-3" />
                  Opacity
                </span>
                <Badge variant="secondary" className="text-xs">{Math.round(opacity * 100)}%</Badge>
              </div>
              <Slider
                value={[opacity]}
                min={0.1}
                max={1}
                step={0.05}
                onValueChange={(value) => setOpacity(value[0])}
                className="w-full"
              />
            </div>

            {/* Zoom Control */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs text-gray-300 flex items-center gap-1">
                  <ZoomIn className="h-3 w-3" />
                  Zoom
                </span>
                <Badge variant="secondary" className="text-xs">{zoom.toFixed(1)}x</Badge>
              </div>
              <Slider
                value={[zoom]}
                min={0.5}
                max={3}
                step={0.1}
                onValueChange={(value) => setZoom(value[0])}
                className="w-full"
              />
            </div>

            {/* Rotation Speed Control */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs text-gray-300 flex items-center gap-1">
                  <RotateCw className="h-3 w-3" />
                  Auto Rotate
                </span>
                <Badge variant={autoRotate ? "default" : "secondary"} className="text-xs">
                  {autoRotate ? "ON" : "OFF"}
                </Badge>
              </div>
              <div className="flex gap-2">
                <Button
                  variant={autoRotate ? "default" : "outline"}
                  size="sm"
                  onClick={() => setAutoRotate(!autoRotate)}
                  className="flex-1 text-xs"
                >
                  {autoRotate ? "Stop" : "Start"}
                </Button>
                <Slider
                  value={[rotationSpeed]}
                  min={0.1}
                  max={5}
                  step={0.1}
                  onValueChange={(value) => setRotationSpeed(value[0])}
                  className="flex-1"
                  disabled={!autoRotate}
                />
              </div>
            </div>

            {/* Lighting Control */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs text-gray-300 flex items-center gap-1">
                  <Layers className="h-3 w-3" />
                  Lighting
                </span>
                <Badge variant="secondary" className="text-xs">{Math.round(lightingIntensity * 100)}%</Badge>
              </div>
              <Slider
                value={[lightingIntensity]}
                min={0.2}
                max={2}
                step={0.1}
                onValueChange={(value) => setLightingIntensity(value[0])}
                className="w-full"
              />
            </div>

            {/* 4D Time Animation Controls */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs text-gray-300 flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  4D Time Animation
                </span>
                <Badge variant={timeAnimation ? "default" : "secondary"} className="text-xs">
                  {timeAnimation ? "ON" : "OFF"}
                </Badge>
              </div>
              <div className="flex gap-2 mb-2">
                <Button
                  variant={timeAnimation ? "default" : "outline"}
                  size="sm"
                  onClick={() => setTimeAnimation(!timeAnimation)}
                  className="flex-1 text-xs"
                >
                  {timeAnimation ? <Pause className="h-3 w-3 mr-1" /> : <Play className="h-3 w-3 mr-1" />}
                  {timeAnimation ? "Pause" : "Play"}
                </Button>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-xs text-gray-400">Speed</span>
                <Slider
                  value={[timeSpeed]}
                  min={0.1}
                  max={3}
                  step={0.1}
                  onValueChange={(value) => setTimeSpeed(value[0])}
                  className="flex-1"
                  disabled={!timeAnimation}
                />
                <span className="text-xs text-gray-400">{timeSpeed.toFixed(1)}x</span>
              </div>
            </div>

            {/* Cross Section Controls */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs text-gray-300 flex items-center gap-1">
                  <Slice className="h-3 w-3" />
                  Cross Section
                </span>
                <Badge variant={crossSectionMode ? "default" : "secondary"} className="text-xs">
                  {crossSectionMode ? "ON" : "OFF"}
                </Badge>
              </div>
              <div className="flex gap-2 mb-2">
                <Button
                  variant={crossSectionMode ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCrossSectionMode(!crossSectionMode)}
                  className="flex-1 text-xs"
                >
                  Toggle Section
                </Button>
              </div>
              {crossSectionMode && (
                <Slider
                  value={[crossSectionPosition]}
                  min={-1}
                  max={1}
                  step={0.05}
                  onValueChange={(value) => setCrossSectionPosition(value[0])}
                  className="w-full"
                />
              )}
            </div>

            {/* Enhanced 4D Features */}
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant={layerPeeling ? "default" : "outline"}
                size="sm"
                onClick={() => setLayerPeeling(!layerPeeling)}
                className="text-xs"
              >
                <Layers className="h-3 w-3 mr-1" />
                Layer Peel
              </Button>
              <Button
                variant={bloodFlowAnimation ? "default" : "outline"}
                size="sm"
                onClick={() => setBloodFlowAnimation(!bloodFlowAnimation)}
                className="text-xs"
              >
                <Activity className="h-3 w-3 mr-1" />
                Blood Flow
              </Button>
              <Button
                variant={tumorGrowthSimulation ? "default" : "outline"}
                size="sm"
                onClick={() => setTumorGrowthSimulation(!tumorGrowthSimulation)}
                className="text-xs"
              >
                <TrendingUp className="h-3 w-3 mr-1" />
                Growth Sim
              </Button>
              <Button
                variant={dynamicLighting ? "default" : "outline"}
                size="sm"
                onClick={() => setDynamicLighting(!dynamicLighting)}
                className="text-xs"
              >
                <Lightbulb className="h-3 w-3 mr-1" />
                Dynamic Light
              </Button>
            </div>

            {/* Toggle Controls */}
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant={showLabels ? "default" : "outline"}
                size="sm"
                onClick={() => setShowLabels(!showLabels)}
                className="text-xs"
              >
                <Info className="h-3 w-3 mr-1" />
                Labels
              </Button>
              <Button
                variant={showWireframe ? "default" : "outline"}
                size="sm"
                onClick={() => setShowWireframe(!showWireframe)}
                className="text-xs"
              >
                <Layers className="h-3 w-3 mr-1" />
                Wireframe
              </Button>
              <Button
                variant={showMeasurements ? "default" : "outline"}
                size="sm"
                onClick={() => setShowMeasurements(!showMeasurements)}
                className="text-xs"
              >
                <Target className="h-3 w-3 mr-1" />
                Measure
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={resetCamera}
                className="text-xs"
              >
                <RotateCcw className="h-3 w-3 mr-1" />
                Reset
              </Button>
            </div>

            {/* Quick Zoom Controls */}
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={zoomOut} className="flex-1 text-xs">
                <ZoomOut className="h-3 w-3 mr-1" />
                Zoom Out
              </Button>
              <Button variant="outline" size="sm" onClick={zoomIn} className="flex-1 text-xs">
                <ZoomIn className="h-3 w-3 mr-1" />
                Zoom In
              </Button>
            </div>
          </div>
        </div>

        {/* Selected Region Info */}
        {selectedRegion && (
          <div className="absolute bottom-4 left-4 bg-gray-900/90 backdrop-blur-sm rounded-lg p-3 z-10 border border-blue-500/20">
            <div className="flex items-center gap-2 mb-2">
              <Target className="h-4 w-4 text-medical-blue" />
              <span className="text-sm font-medium text-white">Selected Region</span>
            </div>
            <p className="text-xs text-gray-300">{selectedRegion}</p>
          </div>
        )}

        {/* Interaction Hint */}
        <div className="absolute bottom-4 right-4 bg-black/50 text-white text-xs py-2 px-4 rounded-full backdrop-blur-sm">
          Click and drag to rotate • Scroll to zoom • Right-click to pan
        </div>
      </div>
    </div>
  )
}

function TumorModel({
  opacity,
  showLabels,
  zoom,
  showWireframe,
  onRegionSelect,
  showMeasurements,
  timeAnimation,
  timeSpeed,
  layerPeeling,
  crossSectionMode,
  crossSectionPosition,
  bloodFlowAnimation,
  tumorGrowthSimulation,
  lightingIntensity
}: {
  opacity: number;
  showLabels: boolean;
  zoom: number;
  showWireframe: boolean;
  onRegionSelect: (region: string | null) => void;
  showMeasurements: boolean;
  timeAnimation: boolean;
  timeSpeed: number;
  layerPeeling: boolean;
  crossSectionMode: boolean;
  crossSectionPosition: number;
  bloodFlowAnimation: boolean;
  tumorGrowthSimulation: boolean;
  lightingIntensity: number;
}) {
  const tumorRef = useRef<THREE.Mesh>(null)
  const healthyRef = useRef<THREE.Mesh>(null)
  const vascularRef = useRef<THREE.Group>(null)
  const [time, setTime] = useState(0)

  useFrame((state, delta) => {
    const adjustedDelta = timeAnimation ? delta * timeSpeed : delta
    setTime(time + adjustedDelta)

    if (tumorRef.current) {
      // Enhanced pulsing animation with 4D time control
      const pulse = 1 + Math.sin(time * 3) * (bloodFlowAnimation ? 0.04 : 0.02)
      tumorRef.current.scale.setScalar(pulse)

      // Tumor growth simulation
      if (tumorGrowthSimulation) {
        const growthFactor = 1 + Math.sin(time * 0.5) * 0.1
        tumorRef.current.scale.multiplyScalar(growthFactor)
      }

      // 4D rotation on multiple axes
      if (timeAnimation) {
        tumorRef.current.rotation.x = Math.sin(time * 0.3) * 0.1
        tumorRef.current.rotation.z = Math.cos(time * 0.2) * 0.05
      }
    }

    if (vascularRef.current) {
      vascularRef.current.rotation.y += timeAnimation ? adjustedDelta * 0.5 : 0.001

      // Enhanced vascular animation
      if (bloodFlowAnimation) {
        vascularRef.current.children.forEach((vessel, index) => {
          if (vessel instanceof THREE.Mesh && vessel.material instanceof THREE.MeshPhysicalMaterial) {
            const flowPhase = time * 2 + index * 0.5
            vessel.material.emissiveIntensity = 0.3 + Math.sin(flowPhase) * 0.2
          }
        })
      }
    }
  })

  const handleTumorClick = () => {
    onRegionSelect("Enhanced 4D Tumor - Grade IV Glioblastoma with Temporal Analysis")
  }

  const handleHealthyClick = () => {
    onRegionSelect("Healthy Brain Tissue - Normal Cortex with 4D Visualization")
  }

  // Create cross-section clipping plane
  const clippingPlane = new THREE.Plane(new THREE.Vector3(1, 0, 0), crossSectionPosition)

  return (
    <group scale={[zoom, zoom, zoom]}>
      {/* Primary Tumor Mass */}
      <mesh
        ref={tumorRef}
        position={[0, 0, 0]}
        onClick={handleTumorClick}
        castShadow
        receiveShadow
      >
        <sphereGeometry args={[1, 64, 64]} />
        <meshPhysicalMaterial
          color="#dc2626"
          transparent
          opacity={opacity}
          roughness={0.4}
          metalness={0.1}
          clearcoat={0.3}
          clearcoatRoughness={0.2}
          emissive="#7f1d1d"
          emissiveIntensity={0.1 * lightingIntensity}
          wireframe={showWireframe}
          clippingPlanes={crossSectionMode ? [clippingPlane] : []}
        />
        {showLabels && (
          <Html position={[1.3, 0, 0]} center>
            <div className="bg-red-900/90 text-white text-xs px-3 py-2 rounded-lg border border-red-500/50 backdrop-blur-sm">
              <div className="font-semibold">Primary Tumor</div>
              <div className="text-red-200">Diameter: 2.4cm</div>
              <div className="text-red-200">Grade: IV</div>
            </div>
          </Html>
        )}
      </mesh>

      {/* Tumor Necrotic Core */}
      <mesh position={[0, 0, 0]}>
        <sphereGeometry args={[0.4, 32, 32]} />
        <meshPhysicalMaterial
          color="#450a0a"
          transparent
          opacity={opacity * 0.8}
          roughness={0.8}
          metalness={0.0}
          emissive="#1c0000"
          emissiveIntensity={0.2}
        />
      </mesh>

      {/* Healthy Brain Tissue */}
      <mesh
        ref={healthyRef}
        position={[0, 0, 0]}
        onClick={handleHealthyClick}
        receiveShadow
      >
        <sphereGeometry args={[1.8, 64, 64]} />
        <meshPhysicalMaterial
          color="#3b82f6"
          transparent
          opacity={0.15}
          roughness={0.2}
          metalness={0.05}
          clearcoat={0.8}
          clearcoatRoughness={0.1}
          transmission={0.3}
          thickness={0.5}
          ior={1.4}
          wireframe={showWireframe}
        />
        {showLabels && (
          <Html position={[0, 2.1, 0]} center>
            <div className="bg-blue-900/90 text-white text-xs px-3 py-2 rounded-lg border border-blue-500/50 backdrop-blur-sm">
              <div className="font-semibold">Healthy Cortex</div>
              <div className="text-blue-200">Normal tissue</div>
            </div>
          </Html>
        )}
      </mesh>

      {/* Vascular Network */}
      <group ref={vascularRef}>
        {Array.from({ length: 12 }, (_, i) => (
          <mesh key={i} position={[
            Math.cos(i * Math.PI / 6) * 1.2,
            Math.sin(i * Math.PI / 6) * 0.3,
            Math.sin(i * Math.PI / 4) * 1.2
          ]}>
            <cylinderGeometry args={[0.02, 0.02, 2, 8]} />
            <meshPhysicalMaterial
              color="#ef4444"
              transparent
              opacity={opacity * 0.6}
              emissive="#dc2626"
              emissiveIntensity={0.3}
            />
          </mesh>
        ))}
      </group>

      {/* Measurement Markers */}
      {showMeasurements && (
        <>
          {/* Tumor diameter measurement */}
          <group>
            <mesh position={[-1, 0, 0]}>
              <sphereGeometry args={[0.05, 8, 8]} />
              <meshBasicMaterial color="#fbbf24" />
            </mesh>
            <mesh position={[1, 0, 0]}>
              <sphereGeometry args={[0.05, 8, 8]} />
              <meshBasicMaterial color="#fbbf24" />
            </mesh>
            <mesh position={[0, 0, 0]} rotation={[0, 0, Math.PI / 2]}>
              <cylinderGeometry args={[0.01, 0.01, 2, 8]} />
              <meshBasicMaterial color="#fbbf24" />
            </mesh>
            <Html position={[0, -0.3, 0]} center>
              <div className="bg-yellow-900/90 text-yellow-100 text-xs px-2 py-1 rounded border border-yellow-500/50">
                2.0 cm
              </div>
            </Html>
          </group>
        </>
      )}

      {/* Coordinate Axes for Medical Reference */}
      {showLabels && (
        <>
          <Text position={[2.5, 0, 0]} color="#ef4444" fontSize={0.15} fontWeight="bold">
            Lateral
          </Text>
          <Text position={[0, 2.5, 0]} color="#22c55e" fontSize={0.15} fontWeight="bold">
            Superior
          </Text>
          <Text position={[0, 0, 2.5]} color="#3b82f6" fontSize={0.15} fontWeight="bold">
            Anterior
          </Text>
        </>
      )}
    </group>
  )
}

function BrainModel({
  opacity,
  showLabels,
  zoom,
  showWireframe,
  onRegionSelect,
  showMeasurements,
  timeAnimation,
  timeSpeed,
  layerPeeling,
  crossSectionMode,
  crossSectionPosition,
  bloodFlowAnimation,
  tumorGrowthSimulation,
  lightingIntensity
}: {
  opacity: number;
  showLabels: boolean;
  zoom: number;
  showWireframe: boolean;
  onRegionSelect: (region: string | null) => void;
  showMeasurements: boolean;
  timeAnimation: boolean;
  timeSpeed: number;
  layerPeeling: boolean;
  crossSectionMode: boolean;
  crossSectionPosition: number;
  bloodFlowAnimation: boolean;
  tumorGrowthSimulation: boolean;
  lightingIntensity: number;
}) {
  const brainRef = useRef<THREE.Mesh>(null)
  const cerebellumRef = useRef<THREE.Mesh>(null)
  const [time, setTime] = useState(0)

  useFrame((state, delta) => {
    setTime(time + delta)

    if (brainRef.current) {
      // Subtle breathing animation
      const breathe = 1 + Math.sin(time * 2) * 0.005
      brainRef.current.scale.setScalar(breathe)
    }
  })

  const handleBrainClick = () => {
    onRegionSelect("Cerebral Cortex - Frontal Lobe")
  }

  const handleTumorClick = () => {
    onRegionSelect("Glioblastoma Multiforme - Right Temporal")
  }

  const handleCerebellumClick = () => {
    onRegionSelect("Cerebellum - Motor Control Center")
  }

  return (
    <group scale={[zoom, zoom, zoom]}>
      {/* Main Brain Structure - Cerebrum */}
      <mesh
        ref={brainRef}
        position={[0, 0.2, 0]}
        scale={[1.3, 1.1, 0.95]}
        onClick={handleBrainClick}
        castShadow
        receiveShadow
      >
        <sphereGeometry args={[1.2, 64, 64]} />
        <meshPhysicalMaterial
          color="#f8b4b4"
          transparent
          opacity={opacity}
          roughness={0.4}
          metalness={0.05}
          clearcoat={0.6}
          clearcoatRoughness={0.2}
          emissive="#fca5a5"
          emissiveIntensity={0.05}
          wireframe={showWireframe}
        />
      </mesh>

      {/* Brain Stem */}
      <mesh position={[0, -0.8, 0]} scale={[0.3, 0.8, 0.3]}>
        <cylinderGeometry args={[0.5, 0.3, 1, 16]} />
        <meshPhysicalMaterial
          color="#e5b4b4"
          transparent
          opacity={opacity * 0.9}
          roughness={0.5}
          metalness={0.02}
        />
      </mesh>

      {/* Cerebellum */}
      <mesh
        ref={cerebellumRef}
        position={[0, -0.6, -0.8]}
        scale={[0.8, 0.6, 0.6]}
        onClick={handleCerebellumClick}
        castShadow
      >
        <sphereGeometry args={[0.8, 32, 32]} />
        <meshPhysicalMaterial
          color="#d4a4a4"
          transparent
          opacity={opacity * 0.8}
          roughness={0.6}
          metalness={0.03}
          wireframe={showWireframe}
        />
        {showLabels && (
          <Html position={[0, -1.2, 0]} center>
            <div className="bg-purple-900/90 text-white text-xs px-3 py-2 rounded-lg border border-purple-500/50 backdrop-blur-sm">
              <div className="font-semibold">Cerebellum</div>
              <div className="text-purple-200">Motor control</div>
            </div>
          </Html>
        )}
      </mesh>

      {/* Tumor in Right Temporal Lobe */}
      <mesh
        position={[0.8, 0.1, 0.2]}
        onClick={handleTumorClick}
        castShadow
      >
        <sphereGeometry args={[0.25, 32, 32]} />
        <meshPhysicalMaterial
          color="#dc2626"
          transparent
          opacity={opacity * 1.2}
          roughness={0.3}
          metalness={0.1}
          emissive="#7f1d1d"
          emissiveIntensity={0.2}
        />
        {showLabels && (
          <Html position={[0.4, 0.3, 0]} center>
            <div className="bg-red-900/90 text-white text-xs px-3 py-2 rounded-lg border border-red-500/50 backdrop-blur-sm">
              <div className="font-semibold">Glioblastoma</div>
              <div className="text-red-200">Right temporal</div>
              <div className="text-red-200">1.8cm diameter</div>
            </div>
          </Html>
        )}
      </mesh>

      {/* Ventricular System */}
      <group>
        {/* Left Ventricle */}
        <mesh position={[-0.3, 0.2, 0]}>
          <sphereGeometry args={[0.15, 16, 16]} />
          <meshPhysicalMaterial
            color="#60a5fa"
            transparent
            opacity={opacity * 0.4}
            transmission={0.8}
            thickness={0.2}
            ior={1.33}
          />
        </mesh>
        {/* Right Ventricle */}
        <mesh position={[0.3, 0.2, 0]}>
          <sphereGeometry args={[0.15, 16, 16]} />
          <meshPhysicalMaterial
            color="#60a5fa"
            transparent
            opacity={opacity * 0.4}
            transmission={0.8}
            thickness={0.2}
            ior={1.33}
          />
        </mesh>
      </group>

      {/* Brain Regions Labels */}
      {showLabels && (
        <>
          <Html position={[0, 1.5, 0.5]} center>
            <div className="bg-blue-900/90 text-white text-xs px-3 py-2 rounded-lg border border-blue-500/50 backdrop-blur-sm">
              <div className="font-semibold">Frontal Lobe</div>
              <div className="text-blue-200">Executive function</div>
            </div>
          </Html>
          <Html position={[-1.5, 0.2, 0]} center>
            <div className="bg-green-900/90 text-white text-xs px-3 py-2 rounded-lg border border-green-500/50 backdrop-blur-sm">
              <div className="font-semibold">Temporal Lobe</div>
              <div className="text-green-200">Memory & language</div>
            </div>
          </Html>
          <Html position={[1.5, 0.2, 0]} center>
            <div className="bg-yellow-900/90 text-white text-xs px-3 py-2 rounded-lg border border-yellow-500/50 backdrop-blur-sm">
              <div className="font-semibold">Parietal Lobe</div>
              <div className="text-yellow-200">Sensory processing</div>
            </div>
          </Html>
          <Html position={[0, 0.2, -1.5]} center>
            <div className="bg-indigo-900/90 text-white text-xs px-3 py-2 rounded-lg border border-indigo-500/50 backdrop-blur-sm">
              <div className="font-semibold">Occipital Lobe</div>
              <div className="text-indigo-200">Visual processing</div>
            </div>
          </Html>
        </>
      )}

      {/* Measurement Markers */}
      {showMeasurements && (
        <>
          {/* Brain width measurement */}
          <group>
            <mesh position={[-1.3, 0.2, 0]}>
              <sphereGeometry args={[0.03, 8, 8]} />
              <meshBasicMaterial color="#10b981" />
            </mesh>
            <mesh position={[1.3, 0.2, 0]}>
              <sphereGeometry args={[0.03, 8, 8]} />
              <meshBasicMaterial color="#10b981" />
            </mesh>
            <mesh position={[0, 0.2, 0]} rotation={[0, 0, Math.PI / 2]}>
              <cylinderGeometry args={[0.005, 0.005, 2.6, 8]} />
              <meshBasicMaterial color="#10b981" />
            </mesh>
            <Html position={[0, 0.5, 0]} center>
              <div className="bg-green-900/90 text-green-100 text-xs px-2 py-1 rounded border border-green-500/50">
                15.6 cm (width)
              </div>
            </Html>
          </group>
        </>
      )}

      {/* Medical Coordinate System */}
      {showLabels && (
        <>
          <Text position={[2.2, 0, 0]} color="#ef4444" fontSize={0.12} fontWeight="bold">
            Right
          </Text>
          <Text position={[-2.2, 0, 0]} color="#ef4444" fontSize={0.12} fontWeight="bold">
            Left
          </Text>
          <Text position={[0, 2.2, 0]} color="#22c55e" fontSize={0.12} fontWeight="bold">
            Superior
          </Text>
          <Text position={[0, -2.2, 0]} color="#22c55e" fontSize={0.12} fontWeight="bold">
            Inferior
          </Text>
          <Text position={[0, 0, 2.2]} color="#3b82f6" fontSize={0.12} fontWeight="bold">
            Anterior
          </Text>
          <Text position={[0, 0, -2.2]} color="#3b82f6" fontSize={0.12} fontWeight="bold">
            Posterior
          </Text>
        </>
      )}
    </group>
  )
}
