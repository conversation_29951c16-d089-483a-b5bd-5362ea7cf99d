"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import {
  Alert<PERSON>riangle,
  Brain,
  Calendar,
  Clock,
  MessageSquare,
  Plus,
  Search,
  Settings,
  TrendingUp,
  Users,
  Zap,
  BarChart3,
  FileText,
  UserPlus,
  CalendarPlus,
  Video,
  Activity,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { MainLayout } from "../layout/main-layout"
import { AIInsightsPanel } from "../ai/ai-insights-panel"
import { LayeredAnatomyModel } from "../visualization/layered-anatomy-model"
import { DetailedBrainTumor } from "../visualization/detailed-brain-tumor"
import { DamagedOrgansModel } from "../visualization/damaged-organs-model"
import { Enhanced4DBrain } from "../visualization/enhanced-4d-brain"
import { ModelIconButton } from "../ui/model-icon-button"
import { ModelViewerModal } from "../ui/model-viewer-modal"

export function InnovativeDashboard() {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [activeModal, setActiveModal] = useState<"anatomy" | "brain" | "organs" | null>(null)
  const [isNewPatientOpen, setIsNewPatientOpen] = useState(false)
  const [isConsultOpen, setIsConsultOpen] = useState(false)
  const [isScheduleOpen, setIsScheduleOpen] = useState(false)
  const router = useRouter()

  useEffect(() => {
    if (typeof window !== "undefined") {
      const timer = setInterval(() => setCurrentTime(new Date()), 1000)
      return () => {
        if (timer) clearInterval(timer)
      }
    }
  }, [])

  const openModal = (type: "anatomy" | "brain" | "organs") => {
    setActiveModal(type)
  }

  const closeModal = () => {
    setActiveModal(null)
  }

  const handleQuickAction = (action: string) => {
    switch (action) {
      case "newPatient":
        setIsNewPatientOpen(true)
        break
      case "schedule":
        setIsScheduleOpen(true)
        break
      case "consult":
        setIsConsultOpen(true)
        break
      case "reports":
        router.push("/reports")
        break
      case "treatment":
        router.push("/treatment")
        break
      case "analytics":
        router.push("/monitoring")
        break
      default:
        console.log(`Quick action clicked: ${action}`)
    }
  }

  const handleNewPatientSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Here you would handle the form submission
    console.log("New patient form submitted")
    setIsNewPatientOpen(false)
    // Optionally redirect to patients page
    router.push("/patients")
  }

  const handleScheduleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log("Schedule appointment form submitted")
    setIsScheduleOpen(false)
  }

  const handleConsultSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log("Consult form submitted")
    setIsConsultOpen(false)
  }

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6">
        {/* Header Section */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-4xl font-bold text-white mb-2">Welcome back, Dr. Priya Sharma</h1>
              <p className="text-slate-300">
                {currentTime.toLocaleDateString("en-US", {
                  weekday: "long",
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                })}
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  placeholder="Search patients, reports..."
                  className="pl-10 w-80 bg-white/10 border-white/20 text-white placeholder:text-slate-400"
                />
              </div>
              <Button variant="outline" size="icon" className="bg-white/10 border-white/20 text-white">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Quick Actions - Enhanced Healthcare Focus */}
          <Card className="medical-card bg-gradient-to-r from-blue-900/20 to-teal-900/20 backdrop-blur-xl border-blue-500/20 mb-8">
            <CardHeader className="pb-2">
              <CardTitle className="text-white text-lg flex items-center gap-2">
                <Activity className="h-5 w-5 text-medical-blue" />
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                {[
                  { icon: <UserPlus />, label: "New Patient", color: "bg-medical-blue", action: "newPatient", description: "Register new patient" },
                  { icon: <CalendarPlus />, label: "Schedule", color: "bg-medical-green", action: "schedule", description: "Book appointment" },
                  { icon: <Video />, label: "Consult", color: "bg-medical-purple", action: "consult", description: "Start consultation" },
                  { icon: <FileText />, label: "Reports", color: "bg-medical-orange", action: "reports", description: "View reports" },
                  { icon: <Zap />, label: "Treatment", color: "bg-medical-teal", action: "treatment", description: "Treatment plans" },
                  { icon: <BarChart3 />, label: "Analytics", color: "bg-medical-red", action: "analytics", description: "View analytics" },
                ].map((action, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    className="medical-button h-20 flex-col space-y-2 bg-white/5 border-white/10 text-white hover:bg-white/10 hover:border-blue-500/50 hover:scale-105 transition-all duration-300 group"
                    onClick={() => handleQuickAction(action.action)}
                  >
                    <div className={`p-2 rounded-lg ${action.color} text-white group-hover:scale-110 transition-transform duration-200`}>
                      {action.icon}
                    </div>
                    <div className="text-center">
                      <span className="text-xs font-medium">{action.label}</span>
                      <p className="text-xs text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                        {action.description}
                      </p>
                    </div>
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <QuickStatCard
              title="Active Patients"
              value="1,247"
              change="+12%"
              icon={<Users className="h-6 w-6" />}
              color="from-blue-500 to-cyan-500"
            />
            <QuickStatCard
              title="Critical Cases"
              value="23"
              change="-8%"
              icon={<AlertTriangle className="h-6 w-6" />}
              color="from-red-500 to-pink-500"
            />
            <QuickStatCard
              title="AI Predictions"
              value="156"
              change="+24%"
              icon={<Brain className="h-6 w-6" />}
              color="from-purple-500 to-indigo-500"
            />
            <QuickStatCard
              title="Success Rate"
              value="94.2%"
              change="+2.1%"
              icon={<TrendingUp className="h-6 w-6" />}
              color="from-green-500 to-emerald-500"
            />
          </div>
        </div>

        {/* 3D Model Icons Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <ModelIconButton
            type="anatomy"
            title="Layered Anatomy"
            description="Interactive 3D model showing multiple anatomical systems including skin, muscles, skeleton, and organs."
            onClick={() => openModal("anatomy")}
          />
          <ModelIconButton
            type="brain"
            title="Brain Tumor Analysis"
            description="Detailed neural visualization with tumor mapping, showing affected pathways and tissue damage."
            onClick={() => openModal("brain")}
          />
          <ModelIconButton
            type="organs"
            title="Radiation Effects"
            description="Visualization of radiation impact on organs, highlighting damage patterns and risk assessment."
            onClick={() => openModal("organs")}
          />
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-12 gap-6">
          {/* AI Insights Panel */}
          <div className="col-span-12">
            <AIInsightsPanel />
          </div>

          {/* Patient Information */}
          <div className="col-span-12 lg:col-span-6">
            <Card className="bg-white/5 backdrop-blur-xl border-white/10">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Users className="h-5 w-5 mr-2 text-blue-400" />
                  Current Patient
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-2xl font-bold text-blue-600">AS</span>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white">Ananya Sharma</h3>
                      <p className="text-slate-400">ID: PT-2024-0156 • Age: 54</p>
                      <p className="text-sm text-slate-500">Glioblastoma Stage IV</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="p-3 bg-white/5 rounded-lg">
                      <p className="text-sm text-slate-400">Tumor Size</p>
                      <p className="text-lg font-semibold text-white">4.2 cm</p>
                    </div>
                    <div className="p-3 bg-white/5 rounded-lg">
                      <p className="text-sm text-slate-400">Treatment Progress</p>
                      <p className="text-lg font-semibold text-green-400">75%</p>
                    </div>
                    <div className="p-3 bg-white/5 rounded-lg">
                      <p className="text-sm text-slate-400">Risk Level</p>
                      <p className="text-lg font-semibold text-yellow-400">High</p>
                    </div>
                    <div className="p-3 bg-white/5 rounded-lg">
                      <p className="text-sm text-slate-400">Response Rate</p>
                      <p className="text-lg font-semibold text-green-400">Excellent</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <div className="col-span-12 lg:col-span-6">
            <RecentActivityPanel />
          </div>
        </div>

        {/* Modals */}
        <ModelViewerModal
          isOpen={activeModal === "anatomy"}
          onClose={closeModal}
          title="Layered Anatomical Model"
          modelType="anatomy"
        >
          <LayeredAnatomyModel />
        </ModelViewerModal>

        <ModelViewerModal
          isOpen={activeModal === "brain"}
          onClose={closeModal}
          title="Enhanced 4D Brain Analysis"
          modelType="brain"
        >
          <Enhanced4DBrain />
        </ModelViewerModal>

        <ModelViewerModal
          isOpen={activeModal === "organs"}
          onClose={closeModal}
          title="Organ Damage Assessment"
          modelType="organs"
        >
          <DamagedOrgansModel />
        </ModelViewerModal>

        {/* Quick Action Modals */}
        <Dialog open={isNewPatientOpen} onOpenChange={setIsNewPatientOpen}>
          <DialogContent className="bg-gray-900 border-gray-700 text-white max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <UserPlus className="h-5 w-5 text-medical-blue" />
                Register New Patient
              </DialogTitle>
            </DialogHeader>
            <form onSubmit={handleNewPatientSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="firstName">First Name</Label>
                  <Input id="firstName" className="medical-input bg-gray-800 border-gray-600 text-white" required />
                </div>
                <div>
                  <Label htmlFor="lastName">Last Name</Label>
                  <Input id="lastName" className="medical-input bg-gray-800 border-gray-600 text-white" required />
                </div>
              </div>
              <div>
                <Label htmlFor="age">Age</Label>
                <Input id="age" type="number" className="medical-input bg-gray-800 border-gray-600 text-white" required />
              </div>
              <div>
                <Label htmlFor="gender">Gender</Label>
                <Select>
                  <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                    <SelectValue placeholder="Select gender" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-600">
                    <SelectItem value="male">Male</SelectItem>
                    <SelectItem value="female">Female</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="phone">Phone Number</Label>
                <Input id="phone" type="tel" className="medical-input bg-gray-800 border-gray-600 text-white" required />
              </div>
              <div>
                <Label htmlFor="email">Email</Label>
                <Input id="email" type="email" className="medical-input bg-gray-800 border-gray-600 text-white" />
              </div>
              <div className="flex gap-2 pt-4">
                <Button type="submit" className="medical-button flex-1">
                  Register Patient
                </Button>
                <Button type="button" variant="outline" onClick={() => setIsNewPatientOpen(false)} className="flex-1">
                  Cancel
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>

        <Dialog open={isScheduleOpen} onOpenChange={setIsScheduleOpen}>
          <DialogContent className="bg-gray-900 border-gray-700 text-white max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <CalendarPlus className="h-5 w-5 text-medical-green" />
                Schedule Appointment
              </DialogTitle>
            </DialogHeader>
            <form onSubmit={handleScheduleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="patient">Patient</Label>
                <Select>
                  <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                    <SelectValue placeholder="Select patient" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-600">
                    <SelectItem value="patient1">Priya Sharma</SelectItem>
                    <SelectItem value="patient2">Arjun Patel</SelectItem>
                    <SelectItem value="patient3">Kavya Singh</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="appointmentType">Appointment Type</Label>
                <Select>
                  <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-600">
                    <SelectItem value="consultation">Consultation</SelectItem>
                    <SelectItem value="followup">Follow-up</SelectItem>
                    <SelectItem value="treatment">Treatment</SelectItem>
                    <SelectItem value="scan">Scan/Imaging</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="date">Date</Label>
                <Input id="date" type="date" className="medical-input bg-gray-800 border-gray-600 text-white" required />
              </div>
              <div>
                <Label htmlFor="time">Time</Label>
                <Input id="time" type="time" className="medical-input bg-gray-800 border-gray-600 text-white" required />
              </div>
              <div>
                <Label htmlFor="notes">Notes</Label>
                <Textarea id="notes" className="medical-input bg-gray-800 border-gray-600 text-white" rows={3} />
              </div>
              <div className="flex gap-2 pt-4">
                <Button type="submit" className="medical-button-secondary flex-1">
                  Schedule Appointment
                </Button>
                <Button type="button" variant="outline" onClick={() => setIsScheduleOpen(false)} className="flex-1">
                  Cancel
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>

        <Dialog open={isConsultOpen} onOpenChange={setIsConsultOpen}>
          <DialogContent className="bg-gray-900 border-gray-700 text-white max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Video className="h-5 w-5 text-medical-purple" />
                Start Consultation
              </DialogTitle>
            </DialogHeader>
            <form onSubmit={handleConsultSubmit} className="space-y-4">
              <div>
                <Label htmlFor="consultPatient">Patient</Label>
                <Select>
                  <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                    <SelectValue placeholder="Select patient" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-600">
                    <SelectItem value="patient1">Priya Sharma</SelectItem>
                    <SelectItem value="patient2">Arjun Patel</SelectItem>
                    <SelectItem value="patient3">Kavya Singh</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="consultType">Consultation Type</Label>
                <Select>
                  <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-600">
                    <SelectItem value="video">Video Call</SelectItem>
                    <SelectItem value="phone">Phone Call</SelectItem>
                    <SelectItem value="inperson">In-Person</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="consultReason">Reason for Consultation</Label>
                <Textarea id="consultReason" className="medical-input bg-gray-800 border-gray-600 text-white" rows={3} placeholder="Brief description of the consultation purpose..." />
              </div>
              <div className="flex gap-2 pt-4">
                <Button type="submit" className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white flex-1">
                  Start Consultation
                </Button>
                <Button type="button" variant="outline" onClick={() => setIsConsultOpen(false)} className="flex-1">
                  Cancel
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  )
}

function QuickStatCard({
  title,
  value,
  change,
  icon,
  color,
}: {
  title: string
  value: string
  change: string
  icon: React.ReactNode
  color: string
}) {
  const isPositive = change.startsWith("+")

  return (
    <Card className="bg-white/5 backdrop-blur-xl border-white/10 overflow-hidden">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-slate-400 text-sm font-medium">{title}</p>
            <p className="text-3xl font-bold text-white mt-2">{value}</p>
            <div className="flex items-center mt-2">
              <span className={`text-sm font-medium ${isPositive ? "text-green-400" : "text-red-400"}`}>{change}</span>
              <span className="text-slate-400 text-sm ml-1">vs last month</span>
            </div>
          </div>
          <div className={`p-3 rounded-xl bg-gradient-to-r ${color} text-white`}>{icon}</div>
        </div>
      </CardContent>
    </Card>
  )
}

function RecentActivityPanel() {
  const activities = [
    {
      id: 1,
      type: "diagnosis",
      patient: "Ananya Sharma",
      action: "New MRI scan analyzed",
      time: "2 minutes ago",
      priority: "high",
    },
    {
      id: 2,
      type: "treatment",
      patient: "Arjun Patel",
      action: "Treatment plan updated",
      time: "15 minutes ago",
      priority: "medium",
    },
    {
      id: 3,
      type: "alert",
      patient: "Kavya Singh",
      action: "Critical threshold reached",
      time: "1 hour ago",
      priority: "high",
    },
  ]

  return (
    <Card className="bg-white/5 backdrop-blur-xl border-white/10">
      <CardHeader>
        <CardTitle className="text-white flex items-center">
          <Clock className="h-5 w-5 mr-2 text-blue-400" />
          Recent Activity
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-center space-x-4 p-3 rounded-lg bg-white/5">
              <div
                className={`w-3 h-3 rounded-full ${activity.priority === "high" ? "bg-red-400" : "bg-yellow-400"}`}
              />
              <div className="flex-1">
                <p className="text-white font-medium">{activity.patient}</p>
                <p className="text-slate-400 text-sm">{activity.action}</p>
              </div>
              <span className="text-slate-400 text-xs">{activity.time}</span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
