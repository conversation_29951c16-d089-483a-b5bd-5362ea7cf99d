#!/usr/bin/env python3
"""
Debug authentication issues
"""

import requests
import json

def test_django_auth():
    """Test Django authentication directly"""
    print("🔍 Testing Django Authentication...")
    
    # Test 1: Check if Django server is running
    try:
        response = requests.get("http://localhost:8000/api/docs/", timeout=5)
        print(f"✅ Django server running: {response.status_code}")
    except Exception as e:
        print(f"❌ Django server not accessible: {e}")
        return False
    
    # Test 2: Test login endpoint
    login_data = {
        "username": "admin",
        "password": "quantnex123"
    }
    
    try:
        print(f"🔐 Testing login with: {login_data}")
        response = requests.post("http://localhost:8000/api/auth/login/", 
                               json=login_data, 
                               headers={"Content-Type": "application/json"},
                               timeout=10)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print(f"Response Text: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Login successful!")
            print(f"User: {data.get('user', {})}")
            print(f"Access Token: {data.get('access', 'Not found')[:50]}...")
            return True
        else:
            print(f"❌ Login failed with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Login request error: {e}")
        return False

def test_frontend_auth():
    """Test frontend authentication"""
    print("\n🌐 Testing Frontend...")
    
    try:
        response = requests.get("http://localhost:3000/login", timeout=5)
        if response.status_code == 200:
            print("✅ Frontend login page accessible")
            return True
        else:
            print(f"❌ Frontend login page: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Frontend error: {e}")
        return False

if __name__ == '__main__':
    print("🚨 CRITICAL AUTH DEBUG")
    print("=" * 40)
    
    django_ok = test_django_auth()
    frontend_ok = test_frontend_auth()
    
    print("\n" + "=" * 40)
    print("📊 DEBUG RESULTS:")
    print(f"Django API: {'✅ WORKING' if django_ok else '❌ FAILED'}")
    print(f"Frontend: {'✅ WORKING' if frontend_ok else '❌ FAILED'}")
    
    if django_ok and frontend_ok:
        print("\n✅ Both systems operational - issue is in integration")
    else:
        print("\n❌ System-level issues detected")
