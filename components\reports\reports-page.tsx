"use client"

import type React from "react"

import { useState } from "react"
import {
  Bar<PERSON>hart3,
  Calendar,
  Download,
  FileText,
  Filter,
  <PERSON><PERSON>hart,
  <PERSON>ader2,
  <PERSON><PERSON>hart,
  Plus,
  Search,
  Share2,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { MainLayout } from "../layout/main-layout"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export function ReportsPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [typeFilter, setTypeFilter] = useState("all")
  const [dateRange, setDateRange] = useState("last-30-days")

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    // Simulate search
    setTimeout(() => {
      setIsLoading(false)
    }, 1000)
  }

  // Mock report data
  const reports = [
    {
      id: "R-1001",
      title: "Monthly Patient Summary",
      type: "patient",
      date: "2025-05-01",
      author: "Dr. Sarah Chen",
      description: "Summary of all patient activities and outcomes for the month of April 2025.",
      icon: <BarChart3 className="h-5 w-5" />,
    },
    {
      id: "R-1002",
      title: "Treatment Efficacy Analysis",
      type: "treatment",
      date: "2025-04-28",
      author: "Dr. Michael Rodriguez",
      description: "Comparative analysis of treatment efficacy across different cancer types and stages.",
      icon: <LineChart className="h-5 w-5" />,
    },
    {
      id: "R-1003",
      title: "Patient Demographics Report",
      type: "patient",
      date: "2025-04-25",
      author: "Dr. Emily Johnson",
      description: "Demographic breakdown of patient population by age, gender, cancer type, and stage.",
      icon: <PieChart className="h-5 w-5" />,
    },
    {
      id: "R-1004",
      title: "Quantum Algorithm Performance",
      type: "research",
      date: "2025-04-22",
      author: "Dr. James Wilson",
      description: "Analysis of quantum algorithm performance in treatment optimization compared to classical methods.",
      icon: <BarChart3 className="h-5 w-5" />,
    },
    {
      id: "R-1005",
      title: "AI Model Accuracy Report",
      type: "ai",
      date: "2025-04-20",
      author: "Dr. Olivia Johnson",
      description: "Evaluation of AI model accuracy in cancer detection and classification.",
      icon: <LineChart className="h-5 w-5" />,
    },
    {
      id: "R-1006",
      title: "Survival Rate Analysis",
      type: "outcome",
      date: "2025-04-18",
      author: "Dr. William Davis",
      description: "Analysis of 5-year survival rates by cancer type, stage, and treatment approach.",
      icon: <LineChart className="h-5 w-5" />,
    },
    {
      id: "R-1007",
      title: "Side Effect Incidence Report",
      type: "treatment",
      date: "2025-04-15",
      author: "Dr. Ava Martinez",
      description: "Incidence and severity of treatment side effects across different protocols.",
      icon: <BarChart3 className="h-5 w-5" />,
    },
    {
      id: "R-1008",
      title: "Research Publication Summary",
      type: "research",
      date: "2025-04-12",
      author: "Dr. Robert Taylor",
      description: "Summary of recent research publications and findings relevant to oncology practice.",
      icon: <FileText className="h-5 w-5" />,
    },
  ]

  // Filter reports based on search query and type filter
  const filteredReports = reports.filter((report) => {
    const matchesSearch =
      searchQuery === "" ||
      report.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      report.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      report.author.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesType = typeFilter === "all" || report.type === typeFilter

    return matchesSearch && matchesType
  })

  return (
    <MainLayout>
      <div className="p-6 space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">Reports</h1>
          <div className="flex space-x-2">
            <Button variant="outline" size="sm">
              <Share2 className="h-4 w-4 mr-2" />
              Share
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button size="sm" className="bg-gradient-to-r from-purple-600 to-pink-600">
              <Plus className="h-4 w-4 mr-2" />
              Generate Report
            </Button>
          </div>
        </div>

        <div className="bg-black/10 backdrop-blur-sm rounded-xl border border-white/10 p-6">
          <div className="flex flex-col md:flex-row justify-between gap-4 mb-6">
            <form onSubmit={handleSearch} className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search reports by title, description, or author..."
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <Button
                type="submit"
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1 h-8"
                disabled={isLoading}
              >
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : "Search"}
              </Button>
            </form>

            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-400">Type:</span>
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Reports</SelectItem>
                    <SelectItem value="patient">Patient</SelectItem>
                    <SelectItem value="treatment">Treatment</SelectItem>
                    <SelectItem value="outcome">Outcome</SelectItem>
                    <SelectItem value="research">Research</SelectItem>
                    <SelectItem value="ai">AI</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-400">Period:</span>
                <Select value={dateRange} onValueChange={setDateRange}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Select date range" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="last-7-days">Last 7 Days</SelectItem>
                    <SelectItem value="last-30-days">Last 30 Days</SelectItem>
                    <SelectItem value="last-90-days">Last 90 Days</SelectItem>
                    <SelectItem value="last-year">Last Year</SelectItem>
                    <SelectItem value="all-time">All Time</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <Tabs defaultValue="all">
            <TabsList className="mb-6">
              <TabsTrigger value="all">All Reports</TabsTrigger>
              <TabsTrigger value="patient">Patient</TabsTrigger>
              <TabsTrigger value="treatment">Treatment</TabsTrigger>
              <TabsTrigger value="research">Research</TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredReports.length === 0 ? (
                  <div className="col-span-3 p-6 text-center text-gray-400">
                    <FileText className="h-12 w-12 mx-auto mb-4 text-gray-600" />
                    <p>No reports found matching your search criteria.</p>
                  </div>
                ) : (
                  filteredReports.map((report) => <ReportCard key={report.id} report={report} />)
                )}
              </div>
            </TabsContent>

            <TabsContent value="patient" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredReports.filter((report) => report.type === "patient").length === 0 ? (
                  <div className="col-span-3 p-6 text-center text-gray-400">
                    <FileText className="h-12 w-12 mx-auto mb-4 text-gray-600" />
                    <p>No patient reports found matching your search criteria.</p>
                  </div>
                ) : (
                  filteredReports
                    .filter((report) => report.type === "patient")
                    .map((report) => <ReportCard key={report.id} report={report} />)
                )}
              </div>
            </TabsContent>

            <TabsContent value="treatment" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredReports.filter((report) => report.type === "treatment").length === 0 ? (
                  <div className="col-span-3 p-6 text-center text-gray-400">
                    <FileText className="h-12 w-12 mx-auto mb-4 text-gray-600" />
                    <p>No treatment reports found matching your search criteria.</p>
                  </div>
                ) : (
                  filteredReports
                    .filter((report) => report.type === "treatment")
                    .map((report) => <ReportCard key={report.id} report={report} />)
                )}
              </div>
            </TabsContent>

            <TabsContent value="research" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredReports.filter((report) => report.type === "research").length === 0 ? (
                  <div className="col-span-3 p-6 text-center text-gray-400">
                    <FileText className="h-12 w-12 mx-auto mb-4 text-gray-600" />
                    <p>No research reports found matching your search criteria.</p>
                  </div>
                ) : (
                  filteredReports
                    .filter((report) => report.type === "research")
                    .map((report) => <ReportCard key={report.id} report={report} />)
                )}
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <div className="bg-black/10 backdrop-blur-sm rounded-xl border border-white/10 p-6">
          <h2 className="text-xl font-semibold mb-4">Recent Reports</h2>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-700">
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-400">Report ID</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-400">Title</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-400">Type</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-400">Date</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-400">Author</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-400">Actions</th>
                </tr>
              </thead>
              <tbody>
                {reports
                  .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
                  .slice(0, 5)
                  .map((report) => (
                    <tr key={report.id} className="border-b border-gray-800 hover:bg-black/20">
                      <td className="px-4 py-4 text-sm">{report.id}</td>
                      <td className="px-4 py-4">
                        <div className="flex items-center">
                          <div className="w-8 h-8 rounded-full bg-cyan-500/20 flex items-center justify-center mr-3">
                            {report.icon}
                          </div>
                          <div className="font-medium">{report.title}</div>
                        </div>
                      </td>
                      <td className="px-4 py-4 text-sm capitalize">{report.type}</td>
                      <td className="px-4 py-4 text-sm">{report.date}</td>
                      <td className="px-4 py-4 text-sm">{report.author}</td>
                      <td className="px-4 py-4 text-sm">
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </Button>
                      </td>
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-black/10 backdrop-blur-sm rounded-xl border border-white/10 p-6">
            <h2 className="text-xl font-semibold mb-4">Report Categories</h2>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-black/20 rounded-lg">
                <div className="flex items-center">
                  <div className="w-8 h-8 rounded-full bg-cyan-500/20 flex items-center justify-center mr-3">
                    <BarChart3 className="h-4 w-4 text-cyan-500" />
                  </div>
                  <span className="text-white">Patient Reports</span>
                </div>
                <span className="text-sm text-gray-400">
                  {reports.filter((r) => r.type === "patient").length} reports
                </span>
              </div>

              <div className="flex items-center justify-between p-3 bg-black/20 rounded-lg">
                <div className="flex items-center">
                  <div className="w-8 h-8 rounded-full bg-purple-500/20 flex items-center justify-center mr-3">
                    <LineChart className="h-4 w-4 text-purple-500" />
                  </div>
                  <span className="text-white">Treatment Reports</span>
                </div>
                <span className="text-sm text-gray-400">
                  {reports.filter((r) => r.type === "treatment").length} reports
                </span>
              </div>

              <div className="flex items-center justify-between p-3 bg-black/20 rounded-lg">
                <div className="flex items-center">
                  <div className="w-8 h-8 rounded-full bg-pink-500/20 flex items-center justify-center mr-3">
                    <PieChart className="h-4 w-4 text-pink-500" />
                  </div>
                  <span className="text-white">Outcome Reports</span>
                </div>
                <span className="text-sm text-gray-400">
                  {reports.filter((r) => r.type === "outcome").length} reports
                </span>
              </div>

              <div className="flex items-center justify-between p-3 bg-black/20 rounded-lg">
                <div className="flex items-center">
                  <div className="w-8 h-8 rounded-full bg-green-500/20 flex items-center justify-center mr-3">
                    <FileText className="h-4 w-4 text-green-500" />
                  </div>
                  <span className="text-white">Research Reports</span>
                </div>
                <span className="text-sm text-gray-400">
                  {reports.filter((r) => r.type === "research").length} reports
                </span>
              </div>
            </div>
          </div>

          <div className="bg-black/10 backdrop-blur-sm rounded-xl border border-white/10 p-6">
            <h2 className="text-xl font-semibold mb-4">Report Templates</h2>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-black/20 rounded-lg hover:bg-black/30 transition-colors cursor-pointer">
                <div className="flex items-center">
                  <div className="w-8 h-8 rounded-full bg-cyan-500/20 flex items-center justify-center mr-3">
                    <BarChart3 className="h-4 w-4 text-cyan-500" />
                  </div>
                  <div>
                    <span className="text-white block">Patient Summary</span>
                    <span className="text-xs text-gray-400">Comprehensive patient status report</span>
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Use
                </Button>
              </div>

              <div className="flex items-center justify-between p-3 bg-black/20 rounded-lg hover:bg-black/30 transition-colors cursor-pointer">
                <div className="flex items-center">
                  <div className="w-8 h-8 rounded-full bg-purple-500/20 flex items-center justify-center mr-3">
                    <LineChart className="h-4 w-4 text-purple-500" />
                  </div>
                  <div>
                    <span className="text-white block">Treatment Efficacy</span>
                    <span className="text-xs text-gray-400">Analysis of treatment outcomes</span>
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Use
                </Button>
              </div>

              <div className="flex items-center justify-between p-3 bg-black/20 rounded-lg hover:bg-black/30 transition-colors cursor-pointer">
                <div className="flex items-center">
                  <div className="w-8 h-8 rounded-full bg-pink-500/20 flex items-center justify-center mr-3">
                    <PieChart className="h-4 w-4 text-pink-500" />
                  </div>
                  <div>
                    <span className="text-white block">Demographic Analysis</span>
                    <span className="text-xs text-gray-400">Patient population breakdown</span>
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Use
                </Button>
              </div>

              <div className="flex items-center justify-between p-3 bg-black/20 rounded-lg hover:bg-black/30 transition-colors cursor-pointer">
                <div className="flex items-center">
                  <div className="w-8 h-8 rounded-full bg-green-500/20 flex items-center justify-center mr-3">
                    <FileText className="h-4 w-4 text-green-500" />
                  </div>
                  <div>
                    <span className="text-white block">Research Summary</span>
                    <span className="text-xs text-gray-400">Summary of research findings</span>
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Use
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}

function ReportCard({ report }: { report: any }) {
  const typeColorMap: Record<string, { bg: string; text: string; border: string }> = {
    patient: {
      bg: "bg-cyan-500/10",
      text: "text-cyan-500",
      border: "border-cyan-500/20",
    },
    treatment: {
      bg: "bg-purple-500/10",
      text: "text-purple-500",
      border: "border-purple-500/20",
    },
    outcome: {
      bg: "bg-pink-500/10",
      text: "text-pink-500",
      border: "border-pink-500/20",
    },
    research: {
      bg: "bg-green-500/10",
      text: "text-green-500",
      border: "border-green-500/20",
    },
    ai: {
      bg: "bg-blue-500/10",
      text: "text-blue-500",
      border: "border-blue-500/20",
    },
  }

  const typeColor = typeColorMap[report.type] || {
    bg: "bg-gray-500/10",
    text: "text-gray-500",
    border: "border-gray-500/20",
  }

  return (
    <Card className="bg-black/20 border-white/10 hover:border-white/20 transition-colors">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div className={`w-10 h-10 rounded-full ${typeColor.bg} flex items-center justify-center`}>{report.icon}</div>
          <div className={`px-2 py-1 text-xs rounded-full ${typeColor.bg} ${typeColor.text} capitalize`}>
            {report.type}
          </div>
        </div>
        <CardTitle className="mt-2">{report.title}</CardTitle>
        <CardDescription className="text-gray-400">
          {report.date} • {report.author}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-gray-300 mb-4">{report.description}</p>
        <div className="flex justify-end">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Download
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
