# Quant-NEX Healthcare Application - Django Backend

## Overview

This is the Django REST API backend for the Quant-NEX Healthcare Application, designed to serve the Next.js frontend with comprehensive medical data management, patient records, and advanced 3D model integration.

## Features

- **Authentication & Authorization**: JWT-based authentication with role-based permissions
- **Patient Management**: Comprehensive patient records with medical history
- **Medical Records**: Detailed medical records with vital signs and attachments
- **Appointment Scheduling**: Advanced appointment management system
- **Dashboard Analytics**: Real-time statistics and metrics
- **3D Model Integration**: Support for medical 3D models and visualizations
- **API Documentation**: Comprehensive API documentation with Swagger/OpenAPI
- **Security**: CORS configuration, input validation, and secure endpoints

## Technology Stack

- **Framework**: Django 5.0.1 with Django REST Framework
- **Database**: PostgreSQL (production) / SQLite (development)
- **Authentication**: JWT with SimpleJWT
- **Documentation**: drf-spectacular (Swagger/OpenAPI)
- **File Storage**: Local storage (development) / AWS S3 (production)
- **Caching**: Redis
- **Task Queue**: Celery (for background tasks)

## Project Structure

```
backend/
├── quantnex/                 # Main Django project
│   ├── settings.py          # Django settings
│   ├── urls.py              # Main URL configuration
│   └── wsgi.py              # WSGI configuration
├── authentication/          # User authentication app
├── patients/                # Patient management app
├── diagnoses/               # Medical diagnoses app
├── treatments/              # Treatment management app
├── reports/                 # Medical reports app
├── monitoring/              # Patient monitoring app
├── medical_models/          # 3D medical models app
├── dashboard/               # Dashboard analytics app
├── requirements.txt         # Python dependencies
├── manage.py               # Django management script
└── README.md               # This file
```

## Installation & Setup

### Prerequisites

- Python 3.9+
- PostgreSQL (for production)
- Redis (for caching and Celery)
- Node.js (for frontend integration)

### 1. Clone and Setup

```bash
# Navigate to backend directory
cd backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Environment Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your configuration
# Update database credentials, secret keys, etc.
```

### 3. Database Setup

```bash
# Create and run migrations
python manage.py makemigrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Load sample data (optional)
python manage.py loaddata fixtures/sample_data.json
```

### 4. Run Development Server

```bash
# Start Django development server
python manage.py runserver 8000

# In another terminal, start Celery worker (optional)
celery -A quantnex worker -l info
```

## API Documentation

Once the server is running, you can access:

- **Swagger UI**: http://localhost:8000/api/docs/
- **ReDoc**: http://localhost:8000/api/redoc/
- **OpenAPI Schema**: http://localhost:8000/api/schema/

## API Endpoints

### Authentication
- `POST /api/auth/register/` - User registration
- `POST /api/auth/login/` - User login
- `POST /api/auth/logout/` - User logout
- `GET /api/auth/profile/` - Get user profile
- `PUT /api/auth/profile/` - Update user profile

### Patients
- `GET /api/patients/` - List patients
- `POST /api/patients/` - Create patient
- `GET /api/patients/{id}/` - Get patient details
- `PUT /api/patients/{id}/` - Update patient
- `DELETE /api/patients/{id}/` - Delete patient

### Medical Records
- `GET /api/patients/{id}/records/` - Get patient medical records
- `POST /api/patients/{id}/records/` - Create medical record

### Appointments
- `GET /api/appointments/` - List appointments
- `POST /api/appointments/` - Create appointment
- `PUT /api/appointments/{id}/` - Update appointment

### Dashboard
- `GET /api/dashboard/overview/` - Dashboard overview
- `GET /api/dashboard/patient-stats/` - Patient statistics
- `GET /api/dashboard/appointments/` - Appointment analytics

## Configuration

### Database Configuration

For PostgreSQL (Production):
```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'quantnex_db',
        'USER': 'quantnex_user',
        'PASSWORD': 'your_password',
        'HOST': 'localhost',
        'PORT': '5432',
    }
}
```

For SQLite (Development):
```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}
```

### CORS Configuration

Update `CORS_ALLOWED_ORIGINS` in settings.py:
```python
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",  # Next.js development
    "https://your-frontend-domain.com",  # Production frontend
]
```

## Deployment

### Production Settings

1. Set `DEBUG = False`
2. Configure production database
3. Set up static file serving
4. Configure CORS for production domains
5. Set up SSL/HTTPS
6. Configure logging

### Docker Deployment

```dockerfile
# Dockerfile example
FROM python:3.9
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["gunicorn", "quantnex.wsgi:application", "--bind", "0.0.0.0:8000"]
```

## Testing

```bash
# Run tests
python manage.py test

# Run with coverage
coverage run --source='.' manage.py test
coverage report
```

## Security Considerations

- JWT tokens for authentication
- CORS properly configured
- Input validation on all endpoints
- SQL injection protection via Django ORM
- XSS protection with Django's built-in features
- CSRF protection for state-changing operations

## Contributing

1. Follow Django coding standards
2. Write tests for new features
3. Update API documentation
4. Follow semantic versioning

## Support

For issues and questions:
- Check the API documentation
- Review Django and DRF documentation
- Create an issue in the project repository

## License

This project is proprietary software for Quant-NEX Healthcare Application.
