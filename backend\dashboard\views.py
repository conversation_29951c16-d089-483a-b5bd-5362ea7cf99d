"""
Dashboard views for Quant-NEX Healthcare Application.
"""
from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.db.models import Count, Q, Avg
from django.utils import timezone
from datetime import datetime, timedelta
from patients.models import Patient, Appointment, MedicalRecord
from patients.serializers import PatientListSerializer, AppointmentSerializer
from .models import DashboardWidget, SystemAlert, UserActivity, SystemMetrics
from .serializers import (
    DashboardWidgetSerializer, SystemAlertSerializer, 
    UserActivitySerializer, DashboardStatsSerializer
)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def dashboard_overview(request):
    """
    Get comprehensive dashboard overview data.
    """
    user = request.user
    today = timezone.now().date()
    
    # Patient statistics
    if user.role == 'doctor':
        patients_queryset = Patient.objects.filter(assigned_doctor=user)
    else:
        patients_queryset = Patient.objects.all()
    
    patient_stats = {
        'total_patients': patients_queryset.count(),
        'active_patients': patients_queryset.filter(status='active').count(),
        'critical_patients': patients_queryset.filter(status='critical').count(),
        'new_patients_this_month': patients_queryset.filter(
            created_at__month=today.month,
            created_at__year=today.year
        ).count(),
    }
    
    # Appointment statistics
    appointments_today = Appointment.objects.filter(
        scheduled_date=today,
        doctor=user if user.role == 'doctor' else None
    )
    
    appointment_stats = {
        'appointments_today': appointments_today.count(),
        'pending_appointments': appointments_today.filter(status='scheduled').count(),
        'completed_appointments': appointments_today.filter(status='completed').count(),
        'upcoming_appointments': Appointment.objects.filter(
            scheduled_date__gt=today,
            doctor=user if user.role == 'doctor' else None
        ).count()[:5],
    }
    
    # Recent activities
    recent_activities = UserActivity.objects.filter(
        user=user
    ).order_by('-created_at')[:10]
    
    # System alerts
    active_alerts = SystemAlert.objects.filter(
        Q(is_global=True) | Q(target_users=user) | Q(target_roles__contains=[user.role]),
        is_active=True,
        start_time__lte=timezone.now(),
        end_time__gte=timezone.now()
    ).distinct()
    
    # Recent patients
    recent_patients = patients_queryset.order_by('-last_visit', '-created_at')[:5]
    
    return Response({
        'patient_stats': patient_stats,
        'appointment_stats': appointment_stats,
        'recent_activities': UserActivitySerializer(recent_activities, many=True).data,
        'active_alerts': SystemAlertSerializer(active_alerts, many=True).data,
        'recent_patients': PatientListSerializer(recent_patients, many=True).data,
        'user_info': {
            'name': user.display_name,
            'role': user.role,
            'specialization': user.specialization,
            'last_login': user.last_login,
        }
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def patient_statistics(request):
    """
    Get detailed patient statistics for dashboard.
    """
    user = request.user
    
    # Base queryset based on user role
    if user.role == 'doctor':
        patients = Patient.objects.filter(assigned_doctor=user)
    else:
        patients = Patient.objects.all()
    
    # Status distribution
    status_stats = patients.values('status').annotate(
        count=Count('id')
    ).order_by('status')
    
    # Cancer type distribution
    cancer_stats = patients.exclude(
        cancer_type__isnull=True
    ).exclude(
        cancer_type__exact=''
    ).values('cancer_type').annotate(
        count=Count('id')
    ).order_by('-count')[:10]
    
    # Age distribution
    age_ranges = {
        '0-18': patients.filter(date_of_birth__gte=timezone.now().date() - timedelta(days=18*365)).count(),
        '19-30': patients.filter(
            date_of_birth__gte=timezone.now().date() - timedelta(days=30*365),
            date_of_birth__lt=timezone.now().date() - timedelta(days=18*365)
        ).count(),
        '31-50': patients.filter(
            date_of_birth__gte=timezone.now().date() - timedelta(days=50*365),
            date_of_birth__lt=timezone.now().date() - timedelta(days=30*365)
        ).count(),
        '51-70': patients.filter(
            date_of_birth__gte=timezone.now().date() - timedelta(days=70*365),
            date_of_birth__lt=timezone.now().date() - timedelta(days=50*365)
        ).count(),
        '70+': patients.filter(
            date_of_birth__lt=timezone.now().date() - timedelta(days=70*365)
        ).count(),
    }
    
    # Gender distribution
    gender_stats = patients.values('gender').annotate(
        count=Count('id')
    ).order_by('gender')
    
    # Treatment progress distribution
    progress_ranges = {
        '0-25%': patients.filter(treatment_progress__lt=25).count(),
        '25-50%': patients.filter(treatment_progress__gte=25, treatment_progress__lt=50).count(),
        '50-75%': patients.filter(treatment_progress__gte=50, treatment_progress__lt=75).count(),
        '75-100%': patients.filter(treatment_progress__gte=75).count(),
    }
    
    return Response({
        'total_patients': patients.count(),
        'status_distribution': list(status_stats),
        'cancer_type_distribution': list(cancer_stats),
        'age_distribution': age_ranges,
        'gender_distribution': list(gender_stats),
        'treatment_progress_distribution': progress_ranges,
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def appointment_analytics(request):
    """
    Get appointment analytics for dashboard.
    """
    user = request.user
    today = timezone.now().date()
    
    # Base queryset
    if user.role == 'doctor':
        appointments = Appointment.objects.filter(doctor=user)
    else:
        appointments = Appointment.objects.all()
    
    # Today's appointments
    today_appointments = appointments.filter(scheduled_date=today)
    
    # This week's appointments
    week_start = today - timedelta(days=today.weekday())
    week_end = week_start + timedelta(days=6)
    week_appointments = appointments.filter(
        scheduled_date__gte=week_start,
        scheduled_date__lte=week_end
    )
    
    # Appointment type distribution
    type_stats = appointments.values('appointment_type').annotate(
        count=Count('id')
    ).order_by('-count')
    
    # Status distribution
    status_stats = appointments.values('status').annotate(
        count=Count('id')
    ).order_by('status')
    
    # Upcoming appointments
    upcoming = appointments.filter(
        scheduled_date__gte=today,
        status__in=['scheduled', 'confirmed']
    ).order_by('scheduled_date', 'scheduled_time')[:10]
    
    return Response({
        'today_total': today_appointments.count(),
        'today_completed': today_appointments.filter(status='completed').count(),
        'today_pending': today_appointments.filter(status__in=['scheduled', 'confirmed']).count(),
        'week_total': week_appointments.count(),
        'type_distribution': list(type_stats),
        'status_distribution': list(status_stats),
        'upcoming_appointments': AppointmentSerializer(upcoming, many=True).data,
    })


class DashboardWidgetViewSet(generics.ListCreateAPIView):
    """
    Dashboard widget management.
    """
    serializer_class = DashboardWidgetSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return DashboardWidget.objects.filter(user=self.request.user)
    
    def perform_create(self, serializer):
        serializer.save(user=self.request.user)


class SystemAlertListView(generics.ListAPIView):
    """
    List active system alerts for the user.
    """
    serializer_class = SystemAlertSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        user = self.request.user
        return SystemAlert.objects.filter(
            Q(is_global=True) | Q(target_users=user) | Q(target_roles__contains=[user.role]),
            is_active=True,
            start_time__lte=timezone.now(),
            end_time__gte=timezone.now()
        ).distinct()


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def system_status(request):
    """
    Get system status and health metrics.
    """
    today = timezone.now().date()
    
    # Get latest metrics
    latest_metrics = {}
    for metric_type, _ in SystemMetrics.METRIC_TYPES:
        latest = SystemMetrics.objects.filter(
            metric_type=metric_type,
            date=today
        ).order_by('-hour').first()
        
        if latest:
            latest_metrics[metric_type] = {
                'value': latest.value,
                'unit': latest.unit,
                'timestamp': latest.created_at
            }
    
    # System health indicators
    health_status = {
        'overall': 'healthy',  # This would be calculated based on various metrics
        'api_response_time': latest_metrics.get('response_time', {}).get('value', 0),
        'error_rate': latest_metrics.get('error_rate', {}).get('value', 0),
        'active_users': latest_metrics.get('user_count', {}).get('value', 0),
        'storage_usage': latest_metrics.get('storage_usage', {}).get('value', 0),
    }
    
    return Response({
        'health_status': health_status,
        'metrics': latest_metrics,
        'last_updated': timezone.now(),
    })
