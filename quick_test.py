import requests

print("🏥 Quant-NEX Integration Status Check")
print("=" * 40)

# Test backend
try:
    response = requests.get("http://localhost:8000/api/docs/", timeout=3)
    print(f"✅ Django Backend: Running (Status: {response.status_code})")
except:
    print("❌ Django Backend: Not responding")

# Test frontend
try:
    response = requests.get("http://localhost:3000", timeout=3)
    print(f"✅ Next.js Frontend: Running (Status: {response.status_code})")
except:
    print("❌ Next.js Frontend: Not responding")

# Test login
try:
    response = requests.post("http://localhost:8000/api/auth/login/", 
                           json={"username": "admin", "password": "quantnex123"}, 
                           timeout=3)
    if response.status_code == 200:
        print("✅ Authentication: Working")
    else:
        print(f"❌ Authentication: Failed ({response.status_code})")
except:
    print("❌ Authentication: Error")

print("\n🎉 Both servers are running and ready for testing!")
print("Frontend: http://localhost:3000")
print("Backend API: http://localhost:8000/api/docs/")
print("Login: admin / quantnex123")
