# Django Backend Requirements for Quant-NEX Healthcare Application

# Core Django
Django==5.0.1
djangorestframework==3.14.0
django-cors-headers==4.3.1

# Authentication & Security
djangorestframework-simplejwt==5.3.0
django-allauth==0.57.0
cryptography==41.0.8

# Database
psycopg2-binary==2.9.9
django-extensions==3.2.3

# API Documentation
drf-spectacular==0.27.0
drf-spectacular-sidecar==2023.12.1

# File Handling & Storage
Pillow==10.1.0
django-storages==1.14.2
boto3==1.34.0

# Data Processing & ML Preparation
numpy==1.24.4
pandas==2.1.4
scikit-learn==1.3.2

# Environment & Configuration
python-decouple==3.8
django-environ==0.11.2

# Utilities
celery==5.3.4
redis==5.0.1
requests==2.31.0

# Development & Testing
pytest==7.4.3
pytest-django==4.7.0
factory-boy==3.3.0
coverage==7.3.2

# Production
gunicorn==21.2.0
whitenoise==6.6.0

# Medical Data Processing (Future ML Integration)
pydicom==2.4.3
nibabel==5.2.0
