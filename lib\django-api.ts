/**
 * Django API integration for Quant-NEX Healthcare Application
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api';

// API Client Configuration
export const apiClient = {
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
};

// Helper function to get authentication headers
export const getAuthHeaders = () => {
  if (typeof window !== 'undefined') {
    const token = localStorage.getItem('access_token');
    return token ? { Authorization: `Bearer ${token}` } : {};
  }
  return {};
};

// Helper function for API requests
const apiRequest = async (endpoint: string, options: RequestInit = {}) => {
  const url = `${apiClient.baseURL}${endpoint}`;
  const config: RequestInit = {
    ...options,
    headers: {
      ...apiClient.headers,
      ...getAuthHeaders(),
      ...options.headers,
    },
  };

  try {
    const response = await fetch(url, config);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
};

// Authentication Service
export const authService = {
  async login(username: string, password: string) {
    const response = await fetch(`${apiClient.baseURL}/auth/login/`, {
      method: 'POST',
      headers: apiClient.headers,
      body: JSON.stringify({ username, password }),
    });
    
    if (response.ok) {
      const data = await response.json();
      if (typeof window !== 'undefined') {
        localStorage.setItem('access_token', data.access);
        localStorage.setItem('refresh_token', data.refresh);
      }
      return data;
    }
    
    throw new Error('Login failed');
  },

  async logout() {
    const refreshToken = typeof window !== 'undefined' 
      ? localStorage.getItem('refresh_token') 
      : null;
    
    if (refreshToken) {
      try {
        await fetch(`${apiClient.baseURL}/auth/logout/`, {
          method: 'POST',
          headers: { ...apiClient.headers, ...getAuthHeaders() },
          body: JSON.stringify({ refresh_token: refreshToken }),
        });
      } catch (error) {
        console.error('Logout error:', error);
      }
    }
    
    if (typeof window !== 'undefined') {
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
    }
  },

  async getProfile() {
    return apiRequest('/auth/profile/');
  },

  async updateProfile(profileData: any) {
    return apiRequest('/auth/profile/', {
      method: 'PUT',
      body: JSON.stringify(profileData),
    });
  },
};

// Patient Service
export const patientService = {
  async getPatients(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return apiRequest(`/patients/${queryString}`);
  },

  async getPatient(id: string) {
    return apiRequest(`/patients/${id}/`);
  },

  async createPatient(patientData: any) {
    return apiRequest('/patients/', {
      method: 'POST',
      body: JSON.stringify(patientData),
    });
  },

  async updatePatient(id: string, patientData: any) {
    return apiRequest(`/patients/${id}/`, {
      method: 'PUT',
      body: JSON.stringify(patientData),
    });
  },

  async deletePatient(id: string) {
    return apiRequest(`/patients/${id}/`, {
      method: 'DELETE',
    });
  },

  async getPatientRecords(patientId: string) {
    return apiRequest(`/patients/${patientId}/records/`);
  },

  async createPatientRecord(patientId: string, recordData: any) {
    return apiRequest(`/patients/${patientId}/records/`, {
      method: 'POST',
      body: JSON.stringify(recordData),
    });
  },

  async getPatientAppointments(patientId: string) {
    return apiRequest(`/patients/${patientId}/appointments/`);
  },

  async searchPatients(searchParams: any) {
    const queryString = new URLSearchParams(searchParams);
    return apiRequest(`/patients/search/?${queryString}`);
  },

  async getPatientStats() {
    return apiRequest('/patients/stats/');
  },
};

// Dashboard Service
export const dashboardService = {
  async getOverview() {
    return apiRequest('/dashboard/overview/');
  },

  async getPatientStats() {
    return apiRequest('/dashboard/patient-stats/');
  },

  async getAppointmentAnalytics() {
    return apiRequest('/dashboard/appointment-analytics/');
  },

  async getSystemStatus() {
    return apiRequest('/dashboard/system-status/');
  },

  async getAlerts() {
    return apiRequest('/dashboard/alerts/');
  },
};

// Appointment Service
export const appointmentService = {
  async getAppointments(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return apiRequest(`/patients/appointments/${queryString}`);
  },

  async createAppointment(appointmentData: any) {
    return apiRequest('/patients/appointments/', {
      method: 'POST',
      body: JSON.stringify(appointmentData),
    });
  },

  async updateAppointment(id: string, appointmentData: any) {
    return apiRequest(`/patients/appointments/${id}/`, {
      method: 'PUT',
      body: JSON.stringify(appointmentData),
    });
  },

  async deleteAppointment(id: string) {
    return apiRequest(`/patients/appointments/${id}/`, {
      method: 'DELETE',
    });
  },
};

// Diagnosis Service
export const diagnosisService = {
  async getDiagnoses(params?: any) {
    const queryString = params ? `?${new URLSearchParams(params)}` : '';
    return apiRequest(`/diagnoses/${queryString}`);
  },

  async createDiagnosis(diagnosisData: any) {
    return apiRequest('/diagnoses/', {
      method: 'POST',
      body: JSON.stringify(diagnosisData),
    });
  },

  async getPatientDiagnoses(patientId: string) {
    return apiRequest(`/diagnoses/patient/${patientId}/`);
  },

  async getImagingStudies() {
    return apiRequest('/diagnoses/imaging/');
  },

  async get3DModelData(studyId: string) {
    return apiRequest(`/diagnoses/imaging/${studyId}/model/`);
  },
};

// Export all services
export default {
  auth: authService,
  patients: patientService,
  dashboard: dashboardService,
  appointments: appointmentService,
  diagnoses: diagnosisService,
};
