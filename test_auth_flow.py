#!/usr/bin/env python3
"""
Test authentication flow for Quant-NEX
"""

import requests
import json

def test_login():
    """Test login functionality"""
    print("🔐 Testing Login...")
    
    url = "http://localhost:8000/api/auth/login/"
    data = {
        "username": "admin",
        "password": "quantnex123"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"Login Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Login successful")
            print(f"   User: {data['user']['display_name']}")
            print(f"   Role: {data['user']['role']}")
            print(f"   Email: {data['user']['email']}")
            return data['access']
        else:
            print(f"❌ Login failed: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def test_register():
    """Test registration functionality"""
    print("\n📝 Testing Registration...")
    
    url = "http://localhost:8000/api/auth/register/"
    data = {
        "username": "<EMAIL>",
        "email": "<EMAIL>",
        "password": "testpass123",
        "first_name": "Test",
        "last_name": "Doctor",
        "role": "doctor"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"Registration Status: {response.status_code}")
        
        if response.status_code == 201:
            data = response.json()
            print("✅ Registration successful")
            print(f"   User: {data['user']['display_name']}")
            print(f"   Role: {data['user']['role']}")
            return data['tokens']['access']
        else:
            print(f"⚠️ Registration response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Registration error: {e}")
        return None

def test_protected_endpoint(token):
    """Test accessing protected endpoints"""
    print("\n🔒 Testing Protected Endpoints...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Test profile endpoint
    try:
        response = requests.get("http://localhost:8000/api/auth/profile/", headers=headers)
        if response.status_code == 200:
            print("✅ Profile endpoint accessible")
        else:
            print(f"❌ Profile endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Profile endpoint error: {e}")
    
    # Test dashboard endpoint
    try:
        response = requests.get("http://localhost:8000/api/dashboard/overview/", headers=headers)
        if response.status_code == 200:
            print("✅ Dashboard endpoint accessible")
        else:
            print(f"⚠️ Dashboard endpoint: {response.status_code}")
    except Exception as e:
        print(f"❌ Dashboard endpoint error: {e}")

def test_frontend_pages():
    """Test frontend page accessibility"""
    print("\n🌐 Testing Frontend Pages...")
    
    pages = [
        ("Landing Page", "http://localhost:3000"),
        ("Login Page", "http://localhost:3000/login"),
        ("Dashboard", "http://localhost:3000/dashboard"),
    ]
    
    for name, url in pages:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {name} accessible")
            else:
                print(f"⚠️ {name}: {response.status_code}")
        except Exception as e:
            print(f"❌ {name} error: {e}")

def main():
    """Run all authentication tests"""
    print("🏥 Quant-NEX Authentication Flow Test")
    print("=" * 50)
    
    # Test login
    login_token = test_login()
    
    # Test registration (might fail if user exists)
    register_token = test_register()
    
    # Test protected endpoints with login token
    if login_token:
        test_protected_endpoint(login_token)
    elif register_token:
        test_protected_endpoint(register_token)
    
    # Test frontend
    test_frontend_pages()
    
    print("\n" + "=" * 50)
    print("🎯 Authentication Test Complete!")
    
    if login_token:
        print("✅ Ready for testing with credentials:")
        print("   Username: admin")
        print("   Password: quantnex123")
        print("\n🌐 Access the application:")
        print("   Frontend: http://localhost:3000")
        print("   Login: http://localhost:3000/login")
        print("   API Docs: http://localhost:8000/api/docs/")

if __name__ == '__main__':
    main()
