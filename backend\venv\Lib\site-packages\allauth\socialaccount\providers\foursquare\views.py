import requests

from allauth.socialaccount.providers.oauth2.views import (
    <PERSON><PERSON>uth<PERSON><PERSON><PERSON><PERSON><PERSON>,
    OAuth2CallbackView,
    OAuth2LoginView,
)

from .provider import FoursquareProvider


class FoursquareOAuth2Adapter(OAuth2Adapter):
    provider_id = FoursquareProvider.id
    access_token_url = "https://foursquare.com/oauth2/access_token"
    # Issue ?? -- this one authenticates over and over again...
    # authorize_url = 'https://foursquare.com/oauth2/authorize'
    authorize_url = "https://foursquare.com/oauth2/authenticate"
    profile_url = "https://api.foursquare.com/v2/users/self"

    def complete_login(self, request, app, token, **kwargs):
        # Foursquare needs a version number for their API requests as
        # documented here
        # https://developer.foursquare.com/overview/versioning
        resp = requests.get(
            self.profile_url,
            params={"oauth_token": token.token, "v": "********"},
        )
        extra_data = resp.json()["response"]["user"]
        return self.get_provider().sociallogin_from_response(request, extra_data)


oauth2_login = OAuth2LoginView.adapter_view(FoursquareOAuth2Adapter)
oauth2_callback = OAuth2CallbackView.adapter_view(FoursquareOAuth2Adapter)
