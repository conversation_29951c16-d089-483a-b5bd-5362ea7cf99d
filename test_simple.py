import requests

# Test simple endpoints
try:
    print("Testing API schema endpoint...")
    response = requests.get("http://localhost:8000/api/schema/")
    print(f"Schema Status: {response.status_code}")
    
    print("\nTesting admin endpoint...")
    response = requests.get("http://localhost:8000/admin/")
    print(f"Admin Status: {response.status_code}")
    
    print("\nTesting auth login endpoint...")
    response = requests.get("http://localhost:8000/api/auth/login/")
    print(f"Auth Login GET Status: {response.status_code}")
    print(f"Response headers: {dict(response.headers)}")
    
except Exception as e:
    print(f"Error: {e}")
