"""
Diagnosis models for Quant-NEX Healthcare Application.
"""
from django.db import models
from django.contrib.auth import get_user_model
from patients.models import Patient

User = get_user_model()


class Diagnosis(models.Model):
    """
    Medical diagnosis model.
    """
    
    DIAGNOSIS_TYPES = [
        ('primary', 'Primary Diagnosis'),
        ('secondary', 'Secondary Diagnosis'),
        ('differential', 'Differential Diagnosis'),
        ('provisional', 'Provisional Diagnosis'),
        ('confirmed', 'Confirmed Diagnosis'),
    ]
    
    SEVERITY_LEVELS = [
        ('mild', 'Mild'),
        ('moderate', 'Moderate'),
        ('severe', 'Severe'),
        ('critical', 'Critical'),
    ]
    
    patient = models.ForeignKey(Patient, on_delete=models.CASCADE, related_name='diagnoses')
    doctor = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='diagnoses')
    
    # Diagnosis Information
    diagnosis_code = models.Char<PERSON>ield(max_length=20, help_text="ICD-10 or similar code")
    diagnosis_name = models.CharField(max_length=200)
    diagnosis_type = models.CharField(max_length=20, choices=DIAGNOSIS_TYPES)
    severity = models.CharField(max_length=20, choices=SEVERITY_LEVELS, blank=True)
    
    # Clinical Details
    symptoms = models.TextField(help_text="Presenting symptoms")
    clinical_findings = models.TextField(help_text="Clinical examination findings")
    differential_diagnoses = models.TextField(blank=True, help_text="Other possible diagnoses")
    
    # Staging and Classification (for cancer)
    cancer_stage = models.CharField(max_length=10, blank=True, help_text="TNM staging")
    cancer_grade = models.CharField(max_length=10, blank=True, help_text="Histological grade")
    metastasis_sites = models.TextField(blank=True, help_text="Sites of metastasis")
    
    # Prognosis
    prognosis = models.TextField(blank=True, help_text="Expected outcome")
    survival_estimate = models.CharField(max_length=100, blank=True)
    
    # Status
    is_active = models.BooleanField(default=True)
    confirmed_date = models.DateField(null=True, blank=True)
    resolved_date = models.DateField(null=True, blank=True)
    
    # Metadata
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'diagnoses'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['patient', 'is_active']),
            models.Index(fields=['diagnosis_code']),
        ]
    
    def __str__(self):
        return f"{self.patient.patient_id} - {self.diagnosis_name}"


class DiagnosticTest(models.Model):
    """
    Diagnostic tests and procedures.
    """
    
    TEST_TYPES = [
        ('blood_test', 'Blood Test'),
        ('imaging', 'Medical Imaging'),
        ('biopsy', 'Biopsy'),
        ('endoscopy', 'Endoscopy'),
        ('genetic_test', 'Genetic Testing'),
        ('pathology', 'Pathology'),
        ('radiology', 'Radiology'),
        ('nuclear_medicine', 'Nuclear Medicine'),
        ('other', 'Other'),
    ]
    
    STATUS_CHOICES = [
        ('ordered', 'Ordered'),
        ('scheduled', 'Scheduled'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    
    patient = models.ForeignKey(Patient, on_delete=models.CASCADE, related_name='diagnostic_tests')
    diagnosis = models.ForeignKey(Diagnosis, on_delete=models.CASCADE, related_name='diagnostic_tests', null=True, blank=True)
    ordered_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='ordered_tests')
    
    # Test Information
    test_type = models.CharField(max_length=30, choices=TEST_TYPES)
    test_name = models.CharField(max_length=200)
    test_code = models.CharField(max_length=50, blank=True)
    description = models.TextField(blank=True)
    
    # Scheduling
    ordered_date = models.DateTimeField(auto_now_add=True)
    scheduled_date = models.DateTimeField(null=True, blank=True)
    completed_date = models.DateTimeField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='ordered')
    
    # Results
    results = models.TextField(blank=True, help_text="Test results and findings")
    normal_range = models.CharField(max_length=200, blank=True)
    is_abnormal = models.BooleanField(default=False)
    critical_values = models.TextField(blank=True)
    
    # Files and Images
    result_files = models.JSONField(default=list, blank=True, help_text="Attached result files")
    images = models.JSONField(default=list, blank=True, help_text="Medical images")
    
    # Metadata
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'diagnostic_tests'
        ordering = ['-ordered_date']
        indexes = [
            models.Index(fields=['patient', 'status']),
            models.Index(fields=['test_type']),
        ]
    
    def __str__(self):
        return f"{self.patient.patient_id} - {self.test_name}"


class ImagingStudy(models.Model):
    """
    Medical imaging studies and 3D model data.
    """
    
    MODALITY_CHOICES = [
        ('CT', 'Computed Tomography'),
        ('MRI', 'Magnetic Resonance Imaging'),
        ('PET', 'Positron Emission Tomography'),
        ('SPECT', 'Single Photon Emission CT'),
        ('US', 'Ultrasound'),
        ('XR', 'X-Ray'),
        ('MG', 'Mammography'),
        ('NM', 'Nuclear Medicine'),
        ('3D_MODEL', '3D Model Reconstruction'),
    ]
    
    patient = models.ForeignKey(Patient, on_delete=models.CASCADE, related_name='imaging_studies')
    diagnostic_test = models.ForeignKey(DiagnosticTest, on_delete=models.CASCADE, related_name='imaging_studies', null=True, blank=True)
    
    # Study Information
    study_id = models.CharField(max_length=50, unique=True)
    modality = models.CharField(max_length=20, choices=MODALITY_CHOICES)
    study_description = models.CharField(max_length=200)
    body_part = models.CharField(max_length=100)
    
    # Technical Parameters
    acquisition_date = models.DateTimeField()
    slice_thickness = models.FloatField(null=True, blank=True, help_text="mm")
    pixel_spacing = models.CharField(max_length=50, blank=True)
    matrix_size = models.CharField(max_length=50, blank=True)
    
    # 3D Model Data
    model_file_path = models.CharField(max_length=500, blank=True)
    model_format = models.CharField(max_length=20, blank=True, help_text="STL, OBJ, PLY, etc.")
    model_size = models.BigIntegerField(null=True, blank=True, help_text="File size in bytes")
    
    # Visualization Parameters
    default_opacity = models.FloatField(default=0.7)
    default_color = models.CharField(max_length=7, default="#ff6b6b", help_text="Hex color code")
    enable_4d_features = models.BooleanField(default=True)
    
    # DICOM Data
    dicom_files = models.JSONField(default=list, blank=True, help_text="DICOM file paths")
    series_uid = models.CharField(max_length=100, blank=True)
    study_uid = models.CharField(max_length=100, blank=True)
    
    # Analysis Results
    findings = models.TextField(blank=True)
    measurements = models.JSONField(default=dict, blank=True, help_text="Measurements and annotations")
    ai_analysis = models.JSONField(default=dict, blank=True, help_text="AI-generated analysis")
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'imaging_studies'
        ordering = ['-acquisition_date']
        indexes = [
            models.Index(fields=['patient', 'modality']),
            models.Index(fields=['study_id']),
        ]
    
    def __str__(self):
        return f"{self.patient.patient_id} - {self.modality} ({self.acquisition_date.date()})"
