"""
Authentication serializers for Quant-NEX Healthcare Application.
"""
from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from .models import User, UserPreferences, UserSession


class UserRegistrationSerializer(serializers.ModelSerializer):
    """
    Serializer for user registration.
    """
    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = [
            'username', 'email', 'password', 'password_confirm',
            'first_name', 'last_name', 'middle_name', 'role',
            'specialization', 'license_number', 'department',
            'hospital_name', 'phone_number'
        ]
    
    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Passwords don't match.")
        return attrs
    
    def create(self, validated_data):
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()
        
        # Create user preferences
        UserPreferences.objects.create(user=user)
        
        return user


class UserLoginSerializer(serializers.Serializer):
    """
    Serializer for user login.
    """
    username = serializers.CharField()
    password = serializers.CharField(write_only=True)
    
    def validate(self, attrs):
        username = attrs.get('username')
        password = attrs.get('password')
        
        if username and password:
            user = authenticate(username=username, password=password)
            if not user:
                raise serializers.ValidationError('Invalid credentials.')
            if not user.is_active:
                raise serializers.ValidationError('User account is disabled.')
            attrs['user'] = user
        else:
            raise serializers.ValidationError('Must include username and password.')
        
        return attrs


class UserProfileSerializer(serializers.ModelSerializer):
    """
    Serializer for user profile information.
    """
    full_name = serializers.ReadOnlyField()
    display_name = serializers.ReadOnlyField()
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'middle_name', 'full_name', 'display_name', 'role',
            'specialization', 'license_number', 'department',
            'hospital_name', 'phone_number', 'emergency_contact',
            'profile_picture', 'bio', 'years_of_experience',
            'is_verified', 'last_login', 'date_joined'
        ]
        read_only_fields = ['id', 'username', 'last_login', 'date_joined', 'is_verified']


class UserPreferencesSerializer(serializers.ModelSerializer):
    """
    Serializer for user preferences.
    """
    class Meta:
        model = UserPreferences
        fields = [
            'theme', 'language', 'default_dashboard_view',
            'show_patient_photos', 'auto_refresh_interval',
            'email_notifications', 'sms_notifications',
            'critical_alerts', 'default_model_quality',
            'enable_4d_features', 'auto_rotate_models'
        ]


class UserSessionSerializer(serializers.ModelSerializer):
    """
    Serializer for user sessions.
    """
    user_display_name = serializers.CharField(source='user.display_name', read_only=True)
    
    class Meta:
        model = UserSession
        fields = [
            'id', 'user_display_name', 'ip_address', 'user_agent',
            'login_time', 'logout_time', 'is_active'
        ]
        read_only_fields = ['id', 'login_time', 'logout_time']


class PasswordChangeSerializer(serializers.Serializer):
    """
    Serializer for password change.
    """
    old_password = serializers.CharField(write_only=True)
    new_password = serializers.CharField(write_only=True, validators=[validate_password])
    new_password_confirm = serializers.CharField(write_only=True)
    
    def validate(self, attrs):
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError("New passwords don't match.")
        return attrs
    
    def validate_old_password(self, value):
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError("Old password is incorrect.")
        return value


class UserListSerializer(serializers.ModelSerializer):
    """
    Serializer for user list (minimal information).
    """
    full_name = serializers.ReadOnlyField()
    display_name = serializers.ReadOnlyField()
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'full_name', 'display_name',
            'role', 'specialization', 'department',
            'is_active', 'is_verified'
        ]
